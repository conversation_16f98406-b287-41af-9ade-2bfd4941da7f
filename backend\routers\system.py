from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db

router = APIRouter(
    prefix="/system",
    tags=["System"],
    responses={404: {"description": "Not found"}},
)

@router.get("/health", response_model=Dict[str, str])
def health_check():
    """
    Simple health check endpoint to verify the API is running.
    """
    return {"status": "healthy"}

@router.get("/info", response_model=Dict[str, str])
def system_info(db: Session = Depends(get_db)):
    """
    Returns basic system information including database connection status.
    """
    try:
        # Test database connection
        db.execute("SELECT 1")
        db_status = "connected"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    return {
        "api_version": "0.1.0",
        "database_status": db_status,
        "environment": "development"
    }
