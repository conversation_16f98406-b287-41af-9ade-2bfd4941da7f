"""
Application Monitoring System
Provides comprehensive monitoring, metrics collection, and health checks.
"""

import time
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import threading
from contextlib import contextmanager

# Try to import psutil, but make it optional
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Performance metric data structure."""
    name: str
    value: float
    unit: str
    timestamp: datetime
    tags: Dict[str, str] = None

@dataclass
class HealthCheck:
    """Health check result data structure."""
    name: str
    status: str  # 'healthy', 'warning', 'critical'
    message: str
    timestamp: datetime
    response_time: float = 0.0
    details: Dict[str, Any] = None

class MetricsCollector:
    """Collects and stores application metrics."""

    def __init__(self, max_metrics: int = 10000):
        self.metrics: deque = deque(maxlen=max_metrics)
        self.counters: Dict[str, int] = defaultdict(int)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.Lock()

    def record_metric(self, name: str, value: float, unit: str = '', tags: Dict[str, str] = None):
        """Record a metric value."""
        with self._lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                unit=unit,
                timestamp=datetime.utcnow(),
                tags=tags or {}
            )
            self.metrics.append(metric)
            logger.debug(f"Recorded metric: {name}={value}{unit}")

    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """Increment a counter metric."""
        with self._lock:
            self.counters[name] += value
            self.record_metric(f"{name}_total", self.counters[name], 'count', tags)

    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """Set a gauge metric value."""
        with self._lock:
            self.gauges[name] = value
            self.record_metric(name, value, 'gauge', tags)

    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a histogram value."""
        with self._lock:
            self.histograms[name].append(value)
            # Keep only last 1000 values
            if len(self.histograms[name]) > 1000:
                self.histograms[name] = self.histograms[name][-1000:]
            self.record_metric(name, value, 'histogram', tags)

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        with self._lock:
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histograms': {
                    name: {
                        'count': len(values),
                        'min': min(values) if values else 0,
                        'max': max(values) if values else 0,
                        'avg': sum(values) / len(values) if values else 0
                    }
                    for name, values in self.histograms.items()
                },
                'total_metrics': len(self.metrics)
            }

class SystemMonitor:
    """Monitors system resources and performance."""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self._monitoring = False
        self._monitor_thread = None

    def start_monitoring(self, interval: int = 30):
        """Start system monitoring in background thread."""
        if self._monitoring:
            return

        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        logger.info(f"Started system monitoring with {interval}s interval")

    def stop_monitoring(self):
        """Stop system monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Stopped system monitoring")

    def _monitor_loop(self, interval: int):
        """Main monitoring loop."""
        while self._monitoring:
            try:
                self._collect_system_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(interval)

    def _collect_system_metrics(self):
        """Collect system metrics."""
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil not available, skipping system metrics collection")
            return

        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics.set_gauge('system_cpu_percent', cpu_percent)

            # Memory metrics
            memory = psutil.virtual_memory()
            self.metrics.set_gauge('system_memory_percent', memory.percent)
            self.metrics.set_gauge('system_memory_used_bytes', memory.used)
            self.metrics.set_gauge('system_memory_available_bytes', memory.available)

            # Disk metrics
            disk = psutil.disk_usage('/')
            self.metrics.set_gauge('system_disk_percent', disk.percent)
            self.metrics.set_gauge('system_disk_used_bytes', disk.used)
            self.metrics.set_gauge('system_disk_free_bytes', disk.free)

            # Network metrics (if available)
            try:
                network = psutil.net_io_counters()
                self.metrics.set_gauge('system_network_bytes_sent', network.bytes_sent)
                self.metrics.set_gauge('system_network_bytes_recv', network.bytes_recv)
            except Exception:
                pass  # Network stats might not be available
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

class HealthChecker:
    """Performs health checks on various system components."""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.checks: Dict[str, Callable] = {}

    def register_check(self, name: str, check_func: Callable):
        """Register a health check function."""
        self.checks[name] = check_func
        logger.info(f"Registered health check: {name}")

    def run_check(self, name: str) -> HealthCheck:
        """Run a specific health check."""
        if name not in self.checks:
            return HealthCheck(
                name=name,
                status='critical',
                message=f"Health check '{name}' not found",
                timestamp=datetime.utcnow()
            )

        start_time = time.time()
        try:
            result = self.checks[name]()
            response_time = time.time() - start_time

            if isinstance(result, HealthCheck):
                result.response_time = response_time
                return result
            else:
                return HealthCheck(
                    name=name,
                    status='healthy',
                    message=str(result),
                    timestamp=datetime.utcnow(),
                    response_time=response_time
                )
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Health check '{name}' failed: {e}")
            return HealthCheck(
                name=name,
                status='critical',
                message=f"Check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                response_time=response_time
            )

    def run_all_checks(self) -> List[HealthCheck]:
        """Run all registered health checks."""
        results = []
        for name in self.checks:
            result = self.run_check(name)
            results.append(result)

            # Record metrics
            self.metrics.record_histogram(f"health_check_{name}_response_time", result.response_time)
            self.metrics.increment_counter(f"health_check_{name}_{result.status}")

        return results

class ApplicationMonitor:
    """Main application monitoring coordinator."""

    def __init__(self):
        self.metrics = MetricsCollector()
        self.system_monitor = SystemMonitor(self.metrics)
        self.health_checker = HealthChecker(self.metrics)
        self._setup_default_health_checks()

    def _setup_default_health_checks(self):
        """Setup default health checks."""

        def database_check():
            """Check database connectivity."""
            try:
                from database import get_db
                db = next(get_db())
                # Simple query to test connection
                db.execute("SELECT 1")
                db.close()
                return HealthCheck(
                    name='database',
                    status='healthy',
                    message='Database connection successful',
                    timestamp=datetime.utcnow()
                )
            except Exception as e:
                return HealthCheck(
                    name='database',
                    status='critical',
                    message=f'Database connection failed: {str(e)}',
                    timestamp=datetime.utcnow()
                )

        def memory_check():
            """Check memory usage."""
            if not PSUTIL_AVAILABLE:
                return HealthCheck(
                    name='memory',
                    status='warning',
                    message='Memory monitoring unavailable (psutil not installed)',
                    timestamp=datetime.utcnow()
                )

            try:
                memory = psutil.virtual_memory()
                if memory.percent > 90:
                    status = 'critical'
                    message = f'High memory usage: {memory.percent}%'
                elif memory.percent > 75:
                    status = 'warning'
                    message = f'Elevated memory usage: {memory.percent}%'
                else:
                    status = 'healthy'
                    message = f'Memory usage normal: {memory.percent}%'

                return HealthCheck(
                    name='memory',
                    status=status,
                    message=message,
                    timestamp=datetime.utcnow(),
                    details={'percent': memory.percent, 'used_gb': memory.used / (1024**3)}
                )
            except Exception as e:
                return HealthCheck(
                    name='memory',
                    status='critical',
                    message=f'Memory check failed: {str(e)}',
                    timestamp=datetime.utcnow()
                )

        def disk_check():
            """Check disk space."""
            if not PSUTIL_AVAILABLE:
                return HealthCheck(
                    name='disk',
                    status='warning',
                    message='Disk monitoring unavailable (psutil not installed)',
                    timestamp=datetime.utcnow()
                )

            try:
                disk = psutil.disk_usage('/')
                if disk.percent > 95:
                    status = 'critical'
                    message = f'Critical disk usage: {disk.percent}%'
                elif disk.percent > 85:
                    status = 'warning'
                    message = f'High disk usage: {disk.percent}%'
                else:
                    status = 'healthy'
                    message = f'Disk usage normal: {disk.percent}%'

                return HealthCheck(
                    name='disk',
                    status=status,
                    message=message,
                    timestamp=datetime.utcnow(),
                    details={'percent': disk.percent, 'free_gb': disk.free / (1024**3)}
                )
            except Exception as e:
                return HealthCheck(
                    name='disk',
                    status='critical',
                    message=f'Disk check failed: {str(e)}',
                    timestamp=datetime.utcnow()
                )

        self.health_checker.register_check('database', database_check)
        self.health_checker.register_check('memory', memory_check)
        self.health_checker.register_check('disk', disk_check)

    def start(self):
        """Start monitoring services."""
        self.system_monitor.start_monitoring()
        logger.info("Application monitoring started")

    def stop(self):
        """Stop monitoring services."""
        self.system_monitor.stop_monitoring()
        logger.info("Application monitoring stopped")

    def get_status(self) -> Dict[str, Any]:
        """Get overall application status."""
        health_checks = self.health_checker.run_all_checks()
        metrics_summary = self.metrics.get_metrics_summary()

        # Determine overall status
        critical_checks = [check for check in health_checks if check.status == 'critical']
        warning_checks = [check for check in health_checks if check.status == 'warning']

        if critical_checks:
            overall_status = 'critical'
        elif warning_checks:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'

        return {
            'status': overall_status,
            'timestamp': datetime.utcnow().isoformat(),
            'health_checks': [asdict(check) for check in health_checks],
            'metrics': metrics_summary,
            'uptime': time.time() - getattr(self, '_start_time', time.time())
        }

# Global monitor instance
app_monitor = ApplicationMonitor()

# Monitoring decorators
def monitor_endpoint(endpoint_name: str):
    """Decorator to monitor API endpoint performance."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                response_time = time.time() - start_time
                app_monitor.metrics.record_histogram(f"endpoint_{endpoint_name}_response_time", response_time)
                app_monitor.metrics.increment_counter(f"endpoint_{endpoint_name}_success")
                return result
            except Exception as e:
                response_time = time.time() - start_time
                app_monitor.metrics.record_histogram(f"endpoint_{endpoint_name}_response_time", response_time)
                app_monitor.metrics.increment_counter(f"endpoint_{endpoint_name}_error")
                raise
        return wrapper
    return decorator

@contextmanager
def monitor_operation(operation_name: str):
    """Context manager to monitor operation performance."""
    start_time = time.time()
    try:
        yield
        duration = time.time() - start_time
        app_monitor.metrics.record_histogram(f"operation_{operation_name}_duration", duration)
        app_monitor.metrics.increment_counter(f"operation_{operation_name}_success")
    except Exception as e:
        duration = time.time() - start_time
        app_monitor.metrics.record_histogram(f"operation_{operation_name}_duration", duration)
        app_monitor.metrics.increment_counter(f"operation_{operation_name}_error")
        raise

# Initialize monitoring on import
app_monitor._start_time = time.time()

# Export main components
__all__ = [
    'app_monitor',
    'monitor_endpoint',
    'monitor_operation',
    'PerformanceMetric',
    'HealthCheck'
]
