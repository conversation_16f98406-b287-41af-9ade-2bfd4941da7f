{"name": "rok-kvk-tracker", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "build:production": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.8.1", "axios": "^1.6.1", "lucide-react": "^0.510.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.18.0", "recharts": "^2.9.3"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.0.2", "vite": "^4.4.5"}}