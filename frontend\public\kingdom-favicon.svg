<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="crownGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="16" cy="16" r="15" fill="url(#shieldGradient)" stroke="#1e40af" stroke-width="2"/>
  
  <!-- Shield Base -->
  <path d="M8 12 L16 8 L24 12 L24 20 C24 24 20 26 16 28 C12 26 8 24 8 20 Z" 
        fill="url(#shieldGradient)" 
        stroke="#1e40af" 
        stroke-width="1"/>
  
  <!-- Crown -->
  <path d="M10 14 L12 11 L14 13 L16 10 L18 13 L20 11 L22 14 L21 16 L11 16 Z" 
        fill="url(#crownGradient)" 
        stroke="#b45309" 
        stroke-width="0.5"/>
  
  <!-- Crown Gems -->
  <circle cx="13" cy="12.5" r="1" fill="url(#gemGradient)"/>
  <circle cx="16" cy="11" r="1.2" fill="url(#gemGradient)"/>
  <circle cx="19" cy="12.5" r="1" fill="url(#gemGradient)"/>
  
  <!-- Kingdom Number -->
  <text x="16" y="23" 
        font-family="Arial, sans-serif" 
        font-size="6" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="white" 
        stroke="#1e40af" 
        stroke-width="0.3">2358</text>
  
  <!-- Highlight Effects -->
  <ellipse cx="13" cy="10" rx="2" ry="1" fill="rgba(255,255,255,0.3)" transform="rotate(-15 13 10)"/>
  <ellipse cx="20" cy="15" rx="1.5" ry="0.8" fill="rgba(255,255,255,0.2)" transform="rotate(20 20 15)"/>
</svg>
