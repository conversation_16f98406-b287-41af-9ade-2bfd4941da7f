import { useEffect } from 'react';

/**
 * Custom hook to dynamically update the document title
 * @param title - The page-specific title
 * @param includeKingdom - Whether to include "Kingdom 2358" prefix (default: true)
 */
export const useDocumentTitle = (title: string, includeKingdom: boolean = true) => {
  useEffect(() => {
    const baseTitle = includeKingdom ? 'Kingdom 2358' : '';
    const separator = includeKingdom && title ? ' - ' : '';
    const fullTitle = `${baseTitle}${separator}${title}`;

    // Store original title
    const originalTitle = document.title;
    document.title = fullTitle;

    // Cleanup function to restore original title when component unmounts
    return () => {
      document.title = originalTitle;
    };
  }, [title, includeKingdom]);
};

/**
 * Predefined page titles for consistency
 */
export const PAGE_TITLES = {
  DASHBOARD: 'Dashboard',
  PERFORMANCE: 'Performance Analytics',
  KVK_HISTORY: 'KvK History',
  SCANS: 'Scan Management',
  UPLOAD: 'Upload Scan',
  LOGIN: 'Sign In',
  UNAUTHORIZED: 'Access Denied',
  CREATE_KVK: 'Create New KvK',
  KVK_DETAIL: 'KvK Details',
  SCAN_DETAIL: 'Scan Details',
  KVK_UPLOAD: 'Upload KvK Scan',
} as const;
