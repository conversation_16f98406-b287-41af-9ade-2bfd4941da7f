from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# DATABASE_URL = "sqlite:///./kvk_tracker.db" # For SQLite
# DATABASE_URL = "postgresql://user:password@host:port/database" # For PostgreSQL

# Prioritize DATABASE_URL from environment variable if available (for production/docker)
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./kvk_tracker.db")

if DATABASE_URL.startswith("postgres"):
    # PostgreSQL with connection pooling
    engine = create_engine(
        DATABASE_URL,
        pool_size=20,          # Number of connections to maintain in the pool
        max_overflow=30,       # Additional connections that can be created on demand
        pool_timeout=30,       # Timeout for getting connection from pool
        pool_recycle=3600,     # Recycle connections after 1 hour
        pool_pre_ping=True,    # Validate connections before use
        echo=False             # Set to True for SQL query logging (development only)
    )
else:
    # For SQLite, use StaticPool and different configuration
    from sqlalchemy.pool import StaticPool
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        pool_pre_ping=True,
        echo=False
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Function to recreate all tables (useful for development)
def recreate_tables():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()