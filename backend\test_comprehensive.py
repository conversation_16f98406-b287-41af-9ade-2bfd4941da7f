"""
Comprehensive test suite for all backend functionality.
"""

import sys
import os
import pytest
import tempfile
import shutil
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database import SessionLocal, Base, engine
    import crud, services, models
    from error_monitoring import error_monitor, ErrorSeverity
    from config import get_settings
    import auth
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the backend directory")
    sys.exit(1)

class TestDatabase:
    """Test database operations and integrity."""
    
    def setup_method(self):
        """Set up test database."""
        self.db = SessionLocal()
        # Create tables if they don't exist
        Base.metadata.create_all(bind=engine)
    
    def teardown_method(self):
        """Clean up after tests."""
        self.db.close()
    
    def test_database_connection(self):
        """Test database connectivity."""
        result = self.db.execute("SELECT 1").scalar()
        assert result == 1
    
    def test_player_crud_operations(self):
        """Test player CRUD operations."""
        # Create player
        player_data = {
            "name": "Test Player",
            "governor_id": "test123",
            "alliance": "Test Alliance"
        }
        
        # Test create
        player = models.Player(**player_data)
        self.db.add(player)
        self.db.commit()
        self.db.refresh(player)
        
        assert player.id is not None
        assert player.name == "Test Player"
        assert player.governor_id == "test123"
        
        # Test read
        retrieved_player = crud.get_player_by_gov_id(self.db, "test123")
        assert retrieved_player is not None
        assert retrieved_player.name == "Test Player"
        
        # Test update
        retrieved_player.alliance = "Updated Alliance"
        self.db.commit()
        
        updated_player = crud.get_player_by_gov_id(self.db, "test123")
        assert updated_player.alliance == "Updated Alliance"
        
        # Clean up
        self.db.delete(player)
        self.db.commit()

class TestCalculations:
    """Test calculation logic and data integrity."""
    
    def setup_method(self):
        """Set up test data."""
        self.db = SessionLocal()
    
    def teardown_method(self):
        """Clean up after tests."""
        self.db.close()
    
    def test_delta_calculation_logic(self):
        """Test delta calculation between scans."""
        # Create test players
        player1 = models.Player(name="Player1", governor_id="gov1", alliance="Alliance1")
        player2 = models.Player(name="Player2", governor_id="gov2", alliance="Alliance2")
        
        self.db.add_all([player1, player2])
        self.db.commit()
        
        # Create baseline scan
        baseline_scan = models.Scan(name="Baseline", is_baseline=True)
        self.db.add(baseline_scan)
        self.db.commit()
        
        # Create current scan
        current_scan = models.Scan(name="Current", is_baseline=False)
        self.db.add(current_scan)
        self.db.commit()
        
        # Create baseline stats
        baseline_stat1 = models.PlayerStat(
            player_id=player1.id,
            scan_id=baseline_scan.id,
            power=1000000,
            total_kill_points=500000,
            dead_troops=10000
        )
        baseline_stat2 = models.PlayerStat(
            player_id=player2.id,
            scan_id=baseline_scan.id,
            power=2000000,
            total_kill_points=1000000,
            dead_troops=20000
        )
        
        # Create current stats
        current_stat1 = models.PlayerStat(
            player_id=player1.id,
            scan_id=current_scan.id,
            power=1200000,  # +200k power
            total_kill_points=700000,  # +200k KP
            dead_troops=15000  # +5k dead
        )
        current_stat2 = models.PlayerStat(
            player_id=player2.id,
            scan_id=current_scan.id,
            power=1800000,  # -200k power (zeroed)
            total_kill_points=1500000,  # +500k KP
            dead_troops=30000  # +10k dead
        )
        
        self.db.add_all([baseline_stat1, baseline_stat2, current_stat1, current_stat2])
        self.db.commit()
        
        # Test delta calculation
        delta_stats = services.calculate_delta_stats(self.db, current_scan, baseline_scan)
        
        assert len(delta_stats) == 2
        
        # Find deltas for each player
        delta1 = next(ds for ds in delta_stats if ds.player_id == player1.id)
        delta2 = next(ds for ds in delta_stats if ds.player_id == player2.id)
        
        # Test Player 1 deltas
        assert delta1.power_delta == 200000
        assert delta1.kill_points_delta == 200000
        assert delta1.dead_troops_delta == 5000
        assert not delta1.is_zeroed
        
        # Test Player 2 deltas (zeroed player)
        assert delta2.power_delta == -200000
        assert delta2.kill_points_delta == 500000
        assert delta2.dead_troops_delta == 10000
        assert delta2.is_zeroed  # Should be marked as zeroed due to power loss
        
        # Clean up
        self.db.query(models.DeltaStat).delete()
        self.db.query(models.PlayerStat).delete()
        self.db.query(models.Scan).delete()
        self.db.query(models.Player).delete()
        self.db.commit()

class TestErrorMonitoring:
    """Test error monitoring and alerting system."""
    
    def test_error_logging(self):
        """Test error logging functionality."""
        # Clear any existing errors
        error_monitor.error_history.clear()
        error_monitor.error_counts.clear()
        
        # Log a test error
        test_error = ValueError("Test error message")
        error_monitor.log_error(
            error=test_error,
            severity=ErrorSeverity.HIGH,
            module="test_module",
            function="test_function",
            user_id="test_user"
        )
        
        # Check that error was logged
        assert len(error_monitor.error_history) == 1
        
        error_event = error_monitor.error_history[0]
        assert error_event.error_type == "ValueError"
        assert error_event.message == "Test error message"
        assert error_event.severity == ErrorSeverity.HIGH
        assert error_event.module == "test_module"
        assert error_event.function == "test_function"
        assert error_event.user_id == "test_user"
    
    def test_error_summary(self):
        """Test error summary generation."""
        # Clear existing errors
        error_monitor.error_history.clear()
        
        # Add multiple test errors
        for i in range(5):
            error_monitor.log_error(
                error=ValueError(f"Test error {i}"),
                severity=ErrorSeverity.MEDIUM,
                module="test_module",
                function="test_function"
            )
        
        # Get summary
        summary = error_monitor.get_error_summary(hours=1)
        
        assert summary["total_errors"] == 5
        assert summary["error_types"]["ValueError"] == 5
        assert summary["severity_breakdown"]["medium"] == 5
        assert summary["module_breakdown"]["test_module"] == 5

class TestSecurity:
    """Test security features and authentication."""
    
    def test_password_hashing(self):
        """Test password hashing and verification."""
        password = "test_password_123"
        
        # Hash password
        hashed = auth.get_password_hash(password)
        
        # Verify password
        assert auth.verify_password(password, hashed)
        assert not auth.verify_password("wrong_password", hashed)
    
    def test_token_creation_and_validation(self):
        """Test JWT token creation and validation."""
        # Create token
        test_data = {"sub": "test_user"}
        token = auth.create_access_token(test_data)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0

class TestConfiguration:
    """Test configuration management."""
    
    def test_settings_loading(self):
        """Test that settings load correctly."""
        settings = get_settings()
        
        assert settings is not None
        assert settings.database_url is not None
        assert settings.secret_key is not None
        assert isinstance(settings.allowed_origins, list)
        assert isinstance(settings.max_requests_per_minute, int)
    
    def test_environment_validation(self):
        """Test environment-specific validation."""
        settings = get_settings()
        
        # Check that environment is valid
        assert settings.environment in ["development", "staging", "production"]
        
        # Check that log level is valid
        assert settings.log_level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

def run_all_tests():
    """Run all tests and return results."""
    print("🧪 COMPREHENSIVE TEST SUITE")
    print("=" * 50)
    
    test_results = {
        "passed": 0,
        "failed": 0,
        "errors": []
    }
    
    test_classes = [
        TestDatabase,
        TestCalculations,
        TestErrorMonitoring,
        TestSecurity,
        TestConfiguration
    ]
    
    for test_class in test_classes:
        class_name = test_class.__name__
        print(f"\n🔍 Running {class_name}...")
        
        try:
            test_instance = test_class()
            
            # Get all test methods
            test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
            
            for method_name in test_methods:
                try:
                    print(f"  ✓ {method_name}")
                    
                    # Setup
                    if hasattr(test_instance, 'setup_method'):
                        test_instance.setup_method()
                    
                    # Run test
                    getattr(test_instance, method_name)()
                    
                    # Teardown
                    if hasattr(test_instance, 'teardown_method'):
                        test_instance.teardown_method()
                    
                    test_results["passed"] += 1
                    
                except Exception as e:
                    print(f"  ❌ {method_name}: {str(e)}")
                    test_results["failed"] += 1
                    test_results["errors"].append(f"{class_name}.{method_name}: {str(e)}")
                    
        except Exception as e:
            print(f"  ❌ Failed to initialize {class_name}: {str(e)}")
            test_results["failed"] += 1
            test_results["errors"].append(f"{class_name} initialization: {str(e)}")
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print(f"✅ Passed: {test_results['passed']}")
    print(f"❌ Failed: {test_results['failed']}")
    print(f"📈 Success Rate: {test_results['passed'] / (test_results['passed'] + test_results['failed']) * 100:.1f}%")
    
    if test_results["errors"]:
        print("\n🚨 ERRORS:")
        for error in test_results["errors"]:
            print(f"  - {error}")
    
    return test_results

if __name__ == "__main__":
    run_all_tests()
