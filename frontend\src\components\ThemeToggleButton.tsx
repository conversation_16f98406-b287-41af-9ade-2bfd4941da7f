import React from 'react';
import { Sun, Moon } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggleButton: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  const buttonBaseClass = "p-2 rounded-md focus:outline-none focus:ring-2 transition-colors duration-200 ease-in-out";
  const buttonLightClass = "bg-slate-200 hover:bg-slate-300 text-slate-700 focus:ring-indigo-500";
  const buttonDarkClass = "bg-gray-700 hover:bg-gray-600 text-yellow-400 focus:ring-yellow-500";

  return (
    <button
      onClick={toggleTheme}
      className={`${buttonBaseClass} ${theme === 'light' ? buttonLightClass : buttonDarkClass}`}
      aria-label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
    >
      {theme === 'light' ? <Moon size={24} /> : <Sun size={24} />}
    </button>
  );
};

export default ThemeToggleButton;
