from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import RedirectResponse
from database import engine, Base, recreate_tables
from routers import players, scans, reports, system, kvks, auth, health, dashboard # Added health and dashboard routers
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request as StarletteRequest
from logging_config import setup_logging, get_logger
from security import SecurityHeaders
import time
import uuid

# Setup logging first
logger = setup_logging()

# Recreate tables to ensure schema is up to date
# recreate_tables()  # Commented out to preserve existing data
# logger.info("Database tables recreated successfully")

app = FastAPI(
    title="Data Website API",
    description="API for tracking player and alliance data.",
    version="0.1.0",
    docs_url="/docs", # Explicitly set docs URL
    redoc_url="/redoc", # Explicitly set redoc URL
    # Increase max request size to 100MB for large Excel files
    max_request_size=100 * 1024 * 1024
)

# Add startup event to initialize default parameters
# Note: @app.on_event is deprecated in FastAPI 0.104+, but still functional
@app.on_event("startup")
async def startup_event():
    """Initialize default parameters on startup."""
    from database import get_db
    import crud

    db = next(get_db())
    try:
        crud.initialize_default_parameters(db)
    finally:
        db.close()

# CORS (Cross-Origin Resource Sharing) middleware
# Configure based on environment
import os
from typing import List

def get_allowed_origins() -> List[str]:
    """Get allowed origins based on environment."""
    env = os.getenv("ENVIRONMENT", "development")

    if env == "production":
        # Production origins - restrict to your actual domains
        return [
            "https://yourdomain.com",
            "https://www.yourdomain.com",
            "https://api.yourdomain.com"
        ]
    elif env == "staging":
        # Staging origins
        return [
            "https://staging.yourdomain.com",
            "http://localhost:3000",
            "http://localhost:5173"
        ]
    else:
        # Development origins
        return [
            "http://localhost:3000",
            "http://localhost:5173",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:5173"
        ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=get_allowed_origins(),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add middleware to handle large file uploads with security
class SecureUploadMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: StarletteRequest, call_next):
        # Set max upload size based on environment
        env = os.getenv("ENVIRONMENT", "development")
        max_size = 50 * 1024 * 1024 if env == "production" else 100 * 1024 * 1024  # 50MB prod, 100MB dev

        # Check content length before processing
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > max_size:
            from fastapi import HTTPException
            raise HTTPException(status_code=413, detail=f"File too large. Maximum size: {max_size // (1024*1024)}MB")

        # Set body size limit
        request._body_size_limit = max_size

        # Add security headers for file uploads
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"

        return response

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: StarletteRequest, call_next):
        # Generate request ID
        request_id = str(uuid.uuid4())
        start_time = time.time()

        # Log request
        logger.info(
            f"Request started",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown")
            }
        )

        # Process request
        try:
            response = await call_next(request)

            # Calculate response time
            process_time = time.time() - start_time

            # Log response
            logger.info(
                f"Request completed",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "status_code": response.status_code,
                    "process_time_ms": round(process_time * 1000, 2)
                }
            )

            # Add request ID and security headers to response
            response.headers["X-Request-ID"] = request_id

            # Add security headers
            security_headers = SecurityHeaders.get_headers()
            for header_name, header_value in security_headers.items():
                response.headers[header_name] = header_value

            return response

        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"Request failed",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "error": str(e),
                    "process_time_ms": round(process_time * 1000, 2)
                }
            )
            raise

# Add compression middleware (should be added early)
app.add_middleware(GZipMiddleware, minimum_size=1000)

app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecureUploadMiddleware)

# Include routers with /api prefix
app.include_router(health.router, prefix="/api") # Health checks (no auth required)
app.include_router(dashboard.router, prefix="/api") # Dashboard data
app.include_router(players.router, prefix="/api")
app.include_router(scans.router, prefix="/api")
app.include_router(kvks.router, prefix="/api") # Added KvK router
app.include_router(auth.router, prefix="/api") # Added auth router
# app.include_router(alliances.router) # Removed as alliances.py does not exist
# app.include_router(admin.router) # Removed as admin.py does not exist
app.include_router(reports.router, prefix="/api")
app.include_router(system.router, prefix="/api") # Ensure system router is included

# Root endpoint to redirect to API docs
@app.get("/", include_in_schema=False)
async def root():
    return RedirectResponse(url="/docs")

# Example of a simple health check endpoint (optional)
@app.get("/health", tags=["System"])
async def health_check():
    return {"status": "healthy"}