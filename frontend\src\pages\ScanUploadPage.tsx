import React, { useState } from 'react';
import FileUpload from '../components/FileUpload';
import { useTheme } from '../contexts/ThemeContext';

const ScanUploadPage: React.FC = () => {
  // const { theme } = useTheme(); // Theme context no longer needed for direct styling
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleUploadSuccess = (data: any) => {
    setSuccessMessage('File uploaded successfully!');
    setErrorMessage(null);
    // Potentially navigate to a results page or show a success message
  };

  const handleUploadError = (error: string) => {
    setErrorMessage(`Upload failed: ${error}`);
    setSuccessMessage(null);
    // Show an error message to the user
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg mt-4">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-center text-blue-600 dark:text-blue-400">
          Upload New Scan Data
        </h1>
        <p className="text-center text-gray-600 dark:text-gray-300 mt-3">
          Upload your CSV or Excel file containing player statistics.
          You can name your scan and mark it as a baseline if needed.
        </p>
      </header>
      <main>
        {successMessage && (
          <div className="mb-4 p-3 rounded-md bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100">
            {successMessage}
          </div>
        )}
        {errorMessage && (
          <div className="mb-4 p-3 rounded-md bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100">
            {errorMessage}
          </div>
        )}
        <FileUpload
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
        />
      </main>
      <footer className="mt-10 text-center text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-4">
        <p className="max-w-2xl mx-auto">
          <span className="font-semibold">Required columns:</span> Player Name, Power, Total Kill Points, Dead Troops
        </p>
        <p className="max-w-2xl mx-auto mt-1">
          <span className="font-semibold">Optional columns:</span> Alliance Tag, Governor ID, T1-T5 Kill Points
        </p>
      </footer>
    </div>
  );
};

export default ScanUploadPage;