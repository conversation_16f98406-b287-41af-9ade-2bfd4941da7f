import React, { memo } from 'react';
// Link is no longer directly used, ButtonLink will use it.
// import { Link } from 'react-router-dom'; 
// useTheme is also not directly used here as ButtonLink handles theme.
// import { useTheme } from '../../contexts/ThemeContext'; 
import ButtonLink from '../common/ButtonLink'; // Import the new ButtonLink

interface AdminActionsSectionProps {
  canUploadScans: boolean;
  canViewAdminPages: boolean;
}

// Define SVG icons as components or constants for reusability
const UploadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
  </svg>
);

const ManageScansIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
    <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
  </svg>
);


const AdminActionsSection: React.FC<AdminActionsSectionProps> = ({ canUploadScans, canViewAdminPages }) => {
  // const { theme } = useTheme(); // ButtonLink now handles theme internally

  if (!canUploadScans && !canViewAdminPages) {
    return null; // Don't render the section if no admin actions are available
  }

  return (
    <div className="mb-8"> {/* Original margin from DashboardPage */}
      <div className="flex flex-wrap gap-3">
        {canUploadScans && (
          <ButtonLink
            to="/upload" 
            variant="primary"
            icon={<UploadIcon />}
          >
            Upload New Scan
          </ButtonLink>
        )}
        {canViewAdminPages && (
          <ButtonLink
            to="/scans"
            variant="secondary"
            icon={<ManageScansIcon />}
          >
            Manage Scans
          </ButtonLink>
        )}
      </div>
    </div>
  );
};

export default memo(AdminActionsSection);
