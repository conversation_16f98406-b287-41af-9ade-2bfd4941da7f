import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import {
  FaLightbulb,
  FaChartLine,
  FaUsers,
  FaShieldAlt,
  FaTrophy,
  FaExclamationTriangle,
  FaArrowRight,
  FaStar,
  FaFire,
  FaTarget
} from 'react-icons/fa';

interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'performance' | 'strategy' | 'analytics' | 'engagement';
  icon: React.ReactNode;
  actionText: string;
}

interface RecommendationsPanelProps {
  players: any[];
  summaryStats?: any;
  isBaselineOnly?: boolean;
}

const RecommendationsPanel: React.FC<RecommendationsPanelProps> = ({
  players,
  summaryStats,
  isBaselineOnly = false
}) => {
  const { theme } = useTheme();

  // Generate dynamic recommendations based on data
  const generateRecommendations = (): Recommendation[] => {
    const recommendations: Recommendation[] = [];

    if (!isBaselineOnly && summaryStats) {
      // Performance-based recommendations
      if (summaryStats.needs_improvement > 0) {
        recommendations.push({
          id: 'underperformers',
          title: 'Address Underperforming Players',
          description: `${summaryStats.needs_improvement} players have zero KP gains. Consider providing training or support.`,
          priority: 'high',
          category: 'performance',
          icon: <FaExclamationTriangle />,
          actionText: 'View Underperformers'
        });
      }

      if (summaryStats.power_loss_players > 0) {
        recommendations.push({
          id: 'power-losses',
          title: 'Investigate Power Losses',
          description: `${summaryStats.power_loss_players} players have lost power. Review their activity and provide assistance.`,
          priority: 'high',
          category: 'strategy',
          icon: <FaShieldAlt />,
          actionText: 'Analyze Power Losses'
        });
      }

      // Positive reinforcement recommendations
      if (summaryStats.total_kp_gain > 100000000) { // 100M+ KP
        recommendations.push({
          id: 'celebrate-success',
          title: 'Celebrate Kingdom Success',
          description: 'Your kingdom has achieved excellent KP gains! Consider recognizing top performers.',
          priority: 'medium',
          category: 'engagement',
          icon: <FaTrophy />,
          actionText: 'View Top Performers'
        });
      }
    }

    // General recommendations
    recommendations.push(
      {
        id: 'alliance-analytics',
        title: 'Implement Alliance Analytics',
        description: 'Track alliance-level performance to identify the strongest and weakest groups.',
        priority: 'medium',
        category: 'analytics',
        icon: <FaUsers />,
        actionText: 'View Alliance Analytics'
      },
      {
        id: 'real-time-monitoring',
        title: 'Set Up Real-Time Monitoring',
        description: 'Enable live data updates to track performance changes as they happen.',
        priority: 'medium',
        category: 'analytics',
        icon: <FaChartLine />,
        actionText: 'Configure Monitoring'
      },
      {
        id: 'performance-targets',
        title: 'Establish Performance Targets',
        description: 'Set kingdom-wide goals for KP gains, power growth, and participation rates.',
        priority: 'low',
        category: 'strategy',
        icon: <FaTarget />,
        actionText: 'Set Targets'
      },
      {
        id: 'player-mentorship',
        title: 'Create Mentorship Program',
        description: 'Pair experienced players with newcomers to improve overall kingdom performance.',
        priority: 'low',
        category: 'engagement',
        icon: <FaStar />,
        actionText: 'Start Program'
      }
    );

    return recommendations.slice(0, 6); // Limit to 6 recommendations
  };

  const recommendations = generateRecommendations();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'blue';
      default: return 'gray';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'performance': return <FaFire />;
      case 'strategy': return <FaShieldAlt />;
      case 'analytics': return <FaChartLine />;
      case 'engagement': return <FaUsers />;
      default: return <FaLightbulb />;
    }
  };

  return (
    <div className={`rounded-2xl shadow-xl p-6 ${
      theme === 'light' ? 'bg-white' : 'bg-gray-800'
    }`}>
      {/* Header */}
      <div className="flex items-center mb-6">
        <div className={`p-3 rounded-xl mr-4 ${
          theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
        }`}>
          <FaLightbulb className="text-xl" />
        </div>
        <div>
          <h3 className={`text-xl font-bold ${
            theme === 'light' ? 'text-gray-900' : 'text-white'
          }`}>
            Smart Recommendations
          </h3>
          <p className={`text-sm ${
            theme === 'light' ? 'text-gray-600' : 'text-gray-400'
          }`}>
            AI-powered insights to improve your kingdom's performance
          </p>
        </div>
      </div>

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {recommendations.map((rec) => {
          const priorityColor = getPriorityColor(rec.priority);
          
          return (
            <div
              key={rec.id}
              className={`p-4 rounded-xl border transition-all duration-200 hover:shadow-lg cursor-pointer ${
                theme === 'light'
                  ? 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
              }`}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg mr-3 ${
                    theme === 'light' ? 'bg-white' : 'bg-gray-800'
                  }`}>
                    {rec.icon}
                  </div>
                  <div>
                    <h4 className={`font-semibold text-sm ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {rec.title}
                    </h4>
                    <div className="flex items-center mt-1">
                      {getCategoryIcon(rec.category)}
                      <span className={`ml-1 text-xs ${
                        theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                      }`}>
                        {rec.category}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Priority Badge */}
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                  priorityColor === 'red'
                    ? 'bg-red-100 text-red-800'
                    : priorityColor === 'orange'
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {rec.priority}
                </span>
              </div>

              {/* Description */}
              <p className={`text-sm mb-4 ${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}>
                {rec.description}
              </p>

              {/* Action Button */}
              <button className={`flex items-center text-sm font-medium transition-colors ${
                theme === 'light'
                  ? 'text-blue-600 hover:text-blue-800'
                  : 'text-blue-400 hover:text-blue-300'
              }`}>
                {rec.actionText}
                <FaArrowRight className="ml-2 text-xs" />
              </button>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className={`mt-6 pt-4 border-t text-center ${
        theme === 'light' ? 'border-gray-200' : 'border-gray-700'
      }`}>
        <p className={`text-xs ${
          theme === 'light' ? 'text-gray-500' : 'text-gray-400'
        }`}>
          Recommendations are updated based on your latest scan data and kingdom performance metrics.
        </p>
      </div>
    </div>
  );
};

export default RecommendationsPanel;
