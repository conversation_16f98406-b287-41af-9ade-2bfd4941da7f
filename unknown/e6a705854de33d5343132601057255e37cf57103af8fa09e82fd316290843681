import React, { useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTheme } from '../contexts/ThemeContext';
import axios from '../config/axios';
import {
  FaUpload,
  FaFileExcel,
  FaCheckCircle,
  FaExclamationTriangle,
  FaCalendarAlt,
  FaFileAlt,
  FaArrowLeft,
  FaCloudUploadAlt
} from 'react-icons/fa';

interface UploadFormData {
  scanName: string;
  scanDate: string;
  isBaseline: boolean;
  file: File | null;
}

const UploadScanPage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize form data - no KvK selection for general uploads
  const [formData, setFormData] = useState<UploadFormData>({
    scanName: '',
    scanDate: new Date().toISOString().split('T')[0],
    isBaseline: false,
    file: null
  });

  const [fileError, setFileError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // No KvK fetching needed for general uploads

  // Upload mutation using real API - no KvK association for general uploads
  const uploadMutation = useMutation({
    mutationFn: async (uploadData: FormData) => {
      const url = `/api/scans/upload?scan_name=${encodeURIComponent(formData.scanName)}&is_baseline=${formData.isBaseline}`;

      const response = await axios.post(url, uploadData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },
    onSuccess: (data) => {
      setSuccessMessage('Scan uploaded successfully!');
      queryClient.invalidateQueries({ queryKey: ['scansList'] });

      // Navigate to scan detail page after a short delay
      setTimeout(() => {
        navigate(`/scans/${data.id}`);
      }, 2000);
    },
    onError: (error: any) => {
      let errorMessage = 'Error uploading file.';
      if (axios.isAxiosError(error) && error.response) {
        errorMessage = error.response.data.detail || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setError(errorMessage);
    }
  });

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFileError(null);

    if (!file) {
      setFormData(prev => ({ ...prev, file: null }));
      return;
    }

    // Check file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const validExtensions = ['xlsx', 'xls', 'csv'];

    if (!fileExtension || !validExtensions.includes(fileExtension)) {
      setFileError(`Please upload a valid Excel or CSV file. Received: ${fileExtension || 'unknown'}`);
      setFormData(prev => ({ ...prev, file: null }));
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    setFormData(prev => ({ ...prev, file }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setFileError(null);

    if (!formData.file) {
      setFileError('Please select a file to upload');
      return;
    }

    if (!formData.scanName.trim()) {
      setError('Please enter a scan name');
      return;
    }

    const uploadData = new FormData();
    uploadData.append('file', formData.file);

    uploadMutation.mutate(uploadData);
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-5xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaCloudUploadAlt className="text-2xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Upload General Scan
                </h1>
                <p className={`text-lg mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Upload player statistics not tied to any specific KvK event
                </p>
              </div>
            </div>

            {/* Upload Steps */}
            <div className="flex items-center space-x-6 mt-6">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  theme === 'light' ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white'
                }`}>
                  1
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  Scan Details
                </span>
              </div>
              <div className={`w-8 h-0.5 ${
                theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
              }`}></div>
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  theme === 'light' ? 'bg-gray-300 text-gray-600' : 'bg-gray-600 text-gray-400'
                }`}>
                  2
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  theme === 'light' ? 'text-gray-500' : 'text-gray-500'
                }`}>
                  File Upload
                </span>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
            <FaCloudUploadAlt className="w-full h-full transform rotate-12" />
          </div>
        </div>

      {successMessage && (
        <div className={`mb-6 p-4 rounded-lg ${theme === 'light' ? 'bg-green-50 text-green-800' : 'bg-green-900/30 text-green-400'}`}>
          {successMessage}
        </div>
      )}

      {error && (
        <div className={`mb-6 p-4 rounded-lg ${theme === 'light' ? 'bg-red-50 text-red-800' : 'bg-red-900/30 text-red-400'}`}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className={`p-6 rounded-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} shadow-md`}>
          <h2 className={`text-xl font-semibold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Scan Information
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="scanName" className={`block text-sm font-medium mb-1 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Scan Name *
              </label>
              <input
                type="text"
                id="scanName"
                value={formData.scanName}
                onChange={(e) => setFormData(prev => ({ ...prev, scanName: e.target.value }))}
                className={`w-full px-3 py-2 rounded-md border ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="e.g., Week 1 Scan"
                required
              />
            </div>

            <div>
              <label htmlFor="scanDate" className={`block text-sm font-medium mb-1 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Scan Date *
              </label>
              <input
                type="date"
                id="scanDate"
                value={formData.scanDate}
                onChange={(e) => setFormData(prev => ({ ...prev, scanDate: e.target.value }))}
                className={`w-full px-3 py-2 rounded-md border ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>
          </div>

          <div className="mt-4">
            <label className={`flex items-center ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              <input
                type="checkbox"
                checked={formData.isBaseline}
                onChange={(e) => setFormData(prev => ({ ...prev, isBaseline: e.target.checked }))}
                className="mr-2 h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium">Mark as baseline scan</span>
            </label>
            <p className={`mt-1 text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              Baseline scans are used as reference points for calculating performance metrics
            </p>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} shadow-md`}>
          <h2 className={`text-xl font-semibold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Upload File
          </h2>

          <div className="space-y-4">
            <div>
              <label htmlFor="file" className={`block text-sm font-medium mb-1 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Select File *
              </label>
              <input
                ref={fileInputRef}
                type="file"
                id="file"
                onChange={handleFileChange}
                accept=".csv,.xlsx,.xls"
                className={`w-full px-3 py-2 rounded-md border ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                } focus:outline-none focus:ring-2 focus:ring-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium ${
                  theme === 'light'
                    ? 'file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
                    : 'file:bg-blue-900 file:text-blue-300 hover:file:bg-blue-800'
                }`}
                required
              />
              {fileError && (
                <p className="mt-1 text-sm text-red-600">{fileError}</p>
              )}
              <p className={`mt-1 text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                Supported formats: CSV, Excel (.xlsx, .xls)
              </p>
            </div>

            {formData.file && (
              <div className={`p-4 rounded-md ${theme === 'light' ? 'bg-gray-50' : 'bg-gray-700/50'}`}>
                <p className={`text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                  Selected file: {formData.file.name}
                </p>
                <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  Size: {(formData.file.size / 1024).toFixed(2)} KB
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-between">
          <Link
            to="/scans"
            className={`px-6 py-2 rounded-md font-medium ${
              theme === 'light'
                ? 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
            } transition-colors`}
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={uploadMutation.isPending || !formData.file || !formData.scanName}
            className={`px-6 py-2 rounded-md font-medium ${
              uploadMutation.isPending || !formData.file || !formData.scanName
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            } transition-colors`}
          >
            {uploadMutation.isPending ? 'Uploading...' : 'Upload Scan'}
          </button>
        </div>
      </form>
      </div>
    </div>
  );
};

export default UploadScanPage;
