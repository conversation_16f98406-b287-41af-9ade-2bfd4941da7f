import React, { memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  change?: number;
  formatter?: (value: number) => string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  change,
  formatter = (val: number) => val.toLocaleString()
}) => {
  const { theme } = useTheme();

  // Format the value if it's a number
  const displayValue = typeof value === 'number' ? formatter(value) : value;

  // Only show change indicator if change is provided and not undefined/null
  const shouldShowChange = change !== undefined && change !== null;

  return (
    <div className={`group relative overflow-hidden p-6 rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
      theme === 'light'
        ? 'bg-gradient-to-br from-white to-gray-50 hover:from-blue-50 hover:to-indigo-50'
        : 'bg-gradient-to-br from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800'
    }`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20"></div>
      </div>

      <div className="relative flex justify-between items-start">
        <div className="flex-1">
          <h2 className={`text-lg font-semibold mb-3 ${
            theme === 'light' ? 'text-gray-700' : 'text-gray-300'
          }`}>
            {title}
          </h2>
          <p className={`text-3xl font-bold mb-2 ${
            theme === 'light' ? 'text-gray-900' : 'text-white'
          }`}>
            {displayValue}
          </p>

          {shouldShowChange && (
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${
              change >= 0
                ? 'bg-green-100 text-green-700'
                : 'bg-red-100 text-red-700'
            }`}>
              {change >= 0 ? (
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              ) : (
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              )}
              {change >= 0 ? '+' : ''}{Math.abs(change).toFixed(1)}%
            </div>
          )}
        </div>

        {icon && (
          <div className={`relative p-4 rounded-2xl transition-all duration-300 group-hover:scale-110 ${
            theme === 'light'
              ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg'
              : 'bg-gradient-to-br from-blue-600 to-indigo-700 text-white shadow-lg'
          }`}>
            <div className="text-2xl">
              {icon}
            </div>
            {/* Glow effect */}
            <div className="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(StatCard);
