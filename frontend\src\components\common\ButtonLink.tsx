import React, { ReactNode } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';

type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'ghost'; // Add more variants as needed

interface ButtonLinkProps extends Omit<LinkProps, 'className'> {
  children: ReactNode;
  variant?: ButtonVariant;
  className?: string; // Allow additional custom classes
  icon?: React.ReactElement; // Optional icon
}

const ButtonLink: React.FC<ButtonLinkProps> = ({
  to,
  children,
  variant = 'primary',
  className = '',
  icon,
  ...rest
}) => {
  const { theme } = useTheme();

  const baseClasses = "inline-flex items-center justify-center px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-150";

  let variantClasses = '';
  switch (variant) {
    case 'secondary':
      variantClasses = theme === 'light'
        ? 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500'
        : 'bg-gray-700 text-white hover:bg-gray-600 focus:ring-gray-600';
      break;
    case 'danger':
      variantClasses = theme === 'light'
        ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
        : 'bg-red-700 text-white hover:bg-red-600 focus:ring-red-600';
      break;
    case 'ghost':
      variantClasses = theme === 'light'
        ? 'text-blue-600 hover:bg-blue-100 focus:ring-blue-500'
        : 'text-blue-400 hover:bg-blue-800 focus:ring-blue-400';
      break;
    case 'primary': // Default
    default:
      variantClasses = theme === 'light'
        ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
        : 'bg-blue-700 text-white hover:bg-blue-600 focus:ring-blue-600';
      break;
  }

  return (
    <Link
      to={to}
      className={`${baseClasses} ${variantClasses} ${className}`}
      {...rest}
    >
      {icon && React.cloneElement(icon, { className: `h-5 w-5 ${children ? 'mr-2' : ''}` })}
      {children}
    </Link>
  );
};

export default ButtonLink;
