import React, { memo } from 'react'; // Import memo
import Leaderboard from '../Leaderboard'; // Assuming Leaderboard is in components directory
import { useTheme } from '../../contexts/ThemeContext';
import { PlayerScanData } from '../../types/dataTypes';

interface LeaderboardsSectionProps {
  topPowerPlayers: PlayerScanData[];
  topKillsPlayers: PlayerScanData[];
  topT45KillsPlayers: PlayerScanData[];
  topDeadsPlayers: PlayerScanData[];
  formatter: (num: number, abbreviated?: boolean) => string;
}

const LeaderboardsSection: React.FC<LeaderboardsSectionProps> = ({
  topPowerPlayers,
  topKillsPlayers,
  topT45KillsPlayers,
  topDeadsPlayers,
  formatter,
}) => {
  const { theme } = useTheme();
  return (
    <section className="mb-10">
      <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
        Detailed Leaderboards
      </h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Leaderboard
          title="Top Players by Power"
          players={topPowerPlayers}
          valueKey="power"
          valueFormatter={(val) => formatter(val, true)}
        />
        <Leaderboard
          title="Top Players by Kills"
          players={topKillsPlayers}
          valueKey="killPoints"
          valueFormatter={(val) => formatter(val, true)}
        />
        <Leaderboard
          title="Top Players by T4-5 Kills"
          players={topT45KillsPlayers}
          valueKey="t45Kills"
          valueFormatter={(val) => formatter(val, true)}
        />
        <Leaderboard
          title="Top Players by Dead Troops"
          players={topDeadsPlayers}
          valueKey="deads"
          valueFormatter={(val) => formatter(val, true)}
        />
      </div>
    </section>
  );
};

export default memo(LeaderboardsSection); // Wrap with memo
