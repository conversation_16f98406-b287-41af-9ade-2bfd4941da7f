import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { Menu, X, Moon, Sun, LogIn, LogOut, User } from 'lucide-react';
import {
  FaCrown,
  FaShieldAlt,
  FaChartLine,
  FaHistory,
  FaUpload,
  FaFileAlt,
  FaUser,
  FaUsers
} from 'react-icons/fa';

const Navbar: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout, isAuthenticated, canUploadScans, canViewAdminPages } = useUser();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={`${
      theme === 'light'
        ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600'
        : 'bg-gradient-to-r from-blue-800 via-indigo-800 to-purple-800'
    } shadow-lg fixed w-full z-50 backdrop-blur-sm`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="flex items-center group">
                <div className="p-2 rounded-xl bg-white/20 backdrop-blur-sm mr-3 group-hover:bg-white/30 transition-all duration-300">
                  <FaCrown className="text-2xl text-white" />
                </div>
                <div>
                  <span className="text-xl font-bold text-white group-hover:text-yellow-200 transition-colors duration-300">
                    KINGDOM 2358
                  </span>
                  <div className="text-xs text-blue-100 font-medium">
                    Asahikawa
                  </div>
                </div>
              </Link>
            </div>
            <div className="hidden sm:ml-8 sm:flex sm:space-x-1">
              <Link
                to="/"
                className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  isActive('/')
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-100 hover:bg-white/10 hover:text-white'
                }`}
              >
                <FaShieldAlt className="mr-2 text-sm" />
                Dashboard
              </Link>

              {canViewAdminPages() && (
                <Link
                  to="/scans"
                  className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                    isActive('/scans')
                      ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                      : 'text-blue-100 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <FaFileAlt className="mr-2 text-sm" />
                  Scans
                </Link>
              )}

              <Link
                to="/performance"
                className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  isActive('/performance')
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-100 hover:bg-white/10 hover:text-white'
                }`}
              >
                <FaChartLine className="mr-2 text-sm" />
                Performance
              </Link>

              <Link
                to="/player-analytics"
                className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  isActive('/player-analytics')
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-100 hover:bg-white/10 hover:text-white'
                }`}
              >
                <FaUser className="mr-2 text-sm" />
                Player Analytics
              </Link>

              <Link
                to="/alliance-analytics"
                className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  isActive('/alliance-analytics')
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-100 hover:bg-white/10 hover:text-white'
                }`}
              >
                <FaUsers className="mr-2 text-sm" />
                Alliance Analytics
              </Link>

              <Link
                to="/kvk-history"
                className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  isActive('/kvk-history')
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-100 hover:bg-white/10 hover:text-white'
                }`}
              >
                <FaHistory className="mr-2 text-sm" />
                KvK History
              </Link>

              {canUploadScans() && (
                <Link
                  to="/upload-scan"
                  className={`group inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                    isActive('/upload-scan')
                      ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm'
                      : 'text-blue-100 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <FaUpload className="mr-2 text-sm" />
                  Upload
                </Link>
              )}
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center space-x-3">
            <button
              onClick={toggleTheme}
              className="p-2 rounded-xl bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300"
            >
              {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
            </button>

            {isAuthenticated ? (
              <div className="flex items-center space-x-3">
                <div className="px-4 py-2 rounded-xl bg-white/20 backdrop-blur-sm">
                  <div className="flex items-center text-white">
                    <div className="p-1 rounded-full bg-white/20 mr-2">
                      <User size={14} />
                    </div>
                    <div>
                      <div className="text-sm font-semibold">{user?.username}</div>
                      <div className="text-xs text-blue-100">{user?.role}</div>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => {
                    logout();
                    navigate('/login');
                  }}
                  className="p-2 rounded-xl bg-red-500/20 backdrop-blur-sm text-red-100 hover:bg-red-500/30 transition-all duration-300"
                  title="Sign out"
                >
                  <LogOut size={20} />
                </button>
              </div>
            ) : (
              <Link
                to="/login"
                className="inline-flex items-center px-4 py-2 rounded-xl bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300 font-medium"
              >
                <LogIn size={16} className="mr-2" />
                Sign In
              </Link>
            )}
          </div>
          <div className="-mr-2 flex items-center sm:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-xl bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300"
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? <X className="block h-6 w-6" /> : <Menu className="block h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="sm:hidden">
          <div className={`pt-2 pb-3 space-y-1 ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <Link
              to="/"
              className={`${isActive('/')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              Dashboard
            </Link>

            {canViewAdminPages() && (
              <Link
                to="/scans"
                className={`${isActive('/scans')
                  ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
                } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
              >
                Scans
              </Link>
            )}
            <Link
              to="/performance"
              className={`${isActive('/performance')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              Performance
            </Link>

            <Link
              to="/player-analytics"
              className={`${isActive('/player-analytics')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              Player Analytics
            </Link>

            <Link
              to="/alliance-analytics"
              className={`${isActive('/alliance-analytics')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              Alliance Analytics
            </Link>

            <Link
              to="/kvk-history"
              className={`${isActive('/kvk-history')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              KvK History
            </Link>
            {canUploadScans() && (
              <Link
                to="/upload-scan"
                className={`${isActive('/upload-scan')
                  ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
                } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
              >
                Upload
              </Link>
            )}
          </div>
          <div className={`pt-4 pb-3 border-t ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
            <div className="flex items-center px-4">
              <button
                onClick={toggleTheme}
                className={`p-2 rounded-full ${theme === 'light' ? 'bg-gray-100 text-gray-500 hover:text-gray-600' : 'bg-gray-700 text-gray-300 hover:text-gray-200'}`}
              >
                {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
              </button>
              <div className="ml-3">
                <div className={`text-base font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>Theme</div>
              </div>
            </div>

            {/* Mobile auth buttons */}
            <div className="mt-3 space-y-1 px-2">
              {isAuthenticated ? (
                <>
                  <div className={`px-4 py-2 text-base font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                    <div className="flex items-center">
                      <User size={16} className="mr-2" />
                      {user?.username} ({user?.role})
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      logout();
                      navigate('/login');
                      setIsMenuOpen(false);
                    }}
                    className={`flex items-center w-full px-4 py-2 text-base font-medium ${
                      theme === 'light'
                        ? 'text-gray-500 hover:text-gray-800 hover:bg-gray-100'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700'
                    } rounded-md`}
                  >
                    <LogOut size={16} className="mr-2" />
                    Sign out
                  </button>
                </>
              ) : (
                <Link
                  to="/login"
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center px-4 py-2 text-base font-medium ${
                    theme === 'light'
                      ? 'text-gray-500 hover:text-gray-800 hover:bg-gray-100'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  } rounded-md`}
                >
                  <LogIn size={16} className="mr-2" />
                  Sign in
                </Link>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
