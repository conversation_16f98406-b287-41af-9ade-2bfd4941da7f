/**
 * Mobile-Optimized Components
 * Provides mobile-first components with touch-friendly interfaces
 */

import React, { useState, useRef, useEffect } from 'react';
import { useViewport, useResponsiveTable, useResponsiveChart } from '../hooks/useResponsiveDesign';
import { useTheme } from '../contexts/ThemeContext';
import { FaChevronDown, FaChevronUp, FaFilter, FaSort, FaSearch } from 'react-icons/fa';

interface MobileTableProps {
  data: any[];
  columns: {
    key: string;
    label: string;
    render?: (value: any, row: any) => React.ReactNode;
    sortable?: boolean;
    mobile?: boolean; // Show on mobile
  }[];
  onRowClick?: (row: any) => void;
  searchable?: boolean;
  sortable?: boolean;
}

export const MobileTable: React.FC<MobileTableProps> = ({
  data,
  columns,
  onRowClick,
  searchable = false,
  sortable = false
}) => {
  const { showAsCards, showScrollableTable, maxVisibleColumns } = useResponsiveTable();
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  // Filter data based on search
  const filteredData = searchTerm
    ? data.filter(row =>
        Object.values(row).some(value =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      )
    : data;

  // Sort data
  const sortedData = sortConfig
    ? [...filteredData].sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
        }
        
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        
        if (sortConfig.direction === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      })
    : filteredData;

  const handleSort = (key: string) => {
    if (!sortable) return;
    
    setSortConfig(current => {
      if (current?.key === key) {
        return current.direction === 'desc' 
          ? { key, direction: 'asc' }
          : { key, direction: 'desc' };
      }
      return { key, direction: 'desc' }; // Always start with highest to lowest
    });
  };

  const toggleRowExpansion = (index: number) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  if (showAsCards) {
    return (
      <div className="mobile-table">
        {searchable && (
          <div className="mb-4">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                  theme === 'light'
                    ? 'border-gray-300 bg-white text-gray-900'
                    : 'border-gray-600 bg-gray-700 text-white'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              />
            </div>
          </div>
        )}

        <div className="space-y-3">
          {sortedData.map((row, index) => {
            const isExpanded = expandedRows.has(index);
            const mobileColumns = columns.filter(col => col.mobile !== false);
            const primaryColumns = mobileColumns.slice(0, 2);
            const secondaryColumns = mobileColumns.slice(2);

            return (
              <div
                key={index}
                className={`rounded-lg border p-4 ${
                  theme === 'light'
                    ? 'border-gray-200 bg-white'
                    : 'border-gray-700 bg-gray-800'
                } ${onRowClick ? 'cursor-pointer hover:shadow-md' : ''}`}
                onClick={() => onRowClick?.(row)}
              >
                {/* Primary info - always visible */}
                <div className="space-y-2">
                  {primaryColumns.map(column => (
                    <div key={column.key} className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-500">
                        {column.label}
                      </span>
                      <span className="text-sm font-semibold">
                        {column.render ? column.render(row[column.key], row) : row[column.key]}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Expandable secondary info */}
                {secondaryColumns.length > 0 && (
                  <>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleRowExpansion(index);
                      }}
                      className="mt-3 flex items-center text-sm text-blue-600 hover:text-blue-800"
                    >
                      {isExpanded ? (
                        <>
                          <FaChevronUp className="mr-1" />
                          Show Less
                        </>
                      ) : (
                        <>
                          <FaChevronDown className="mr-1" />
                          Show More
                        </>
                      )}
                    </button>

                    {isExpanded && (
                      <div className="mt-3 pt-3 border-t border-gray-200 space-y-2">
                        {secondaryColumns.map(column => (
                          <div key={column.key} className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-500">
                              {column.label}
                            </span>
                            <span className="text-sm">
                              {column.render ? column.render(row[column.key], row) : row[column.key]}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Scrollable table for tablets
  if (showScrollableTable) {
    const visibleColumns = columns.slice(0, maxVisibleColumns);

    return (
      <div className="mobile-table">
        {searchable && (
          <div className="mb-4">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                  theme === 'light'
                    ? 'border-gray-300 bg-white text-gray-900'
                    : 'border-gray-600 bg-gray-700 text-white'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              />
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className={`min-w-full ${
            theme === 'light' ? 'bg-white' : 'bg-gray-800'
          }`}>
            <thead>
              <tr className={theme === 'light' ? 'bg-gray-50' : 'bg-gray-700'}>
                {visibleColumns.map(column => (
                  <th
                    key={column.key}
                    className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    } ${column.sortable && sortable ? 'cursor-pointer hover:bg-gray-100' : ''}`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                      {column.sortable && sortable && (
                        <FaSort className="text-gray-400" />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {sortedData.map((row, index) => (
                <tr
                  key={index}
                  className={`${
                    onRowClick ? 'cursor-pointer hover:bg-gray-50' : ''
                  } ${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'}`}
                  onClick={() => onRowClick?.(row)}
                >
                  {visibleColumns.map(column => (
                    <td
                      key={column.key}
                      className={`px-4 py-4 whitespace-nowrap text-sm ${
                        theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                      }`}
                    >
                      {column.render ? column.render(row[column.key], row) : row[column.key]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  // Full table for desktop (fallback)
  return (
    <div className="overflow-x-auto">
      <table className={`min-w-full ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
        <thead>
          <tr className={theme === 'light' ? 'bg-gray-50' : 'bg-gray-700'}>
            {columns.map(column => (
              <th
                key={column.key}
                className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                } ${column.sortable && sortable ? 'cursor-pointer hover:bg-gray-100' : ''}`}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.label}</span>
                  {column.sortable && sortable && (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {sortedData.map((row, index) => (
            <tr
              key={index}
              className={`${
                onRowClick ? 'cursor-pointer hover:bg-gray-50' : ''
              } ${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'}`}
              onClick={() => onRowClick?.(row)}
            >
              {columns.map(column => (
                <td
                  key={column.key}
                  className={`px-6 py-4 whitespace-nowrap text-sm ${
                    theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                  }`}
                >
                  {column.render ? column.render(row[column.key], row) : row[column.key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

interface TouchFriendlyButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

export const TouchFriendlyButton: React.FC<TouchFriendlyButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = ''
}) => {
  const { theme } = useTheme();
  const { isMobile } = useViewport();

  const baseClasses = `
    inline-flex items-center justify-center
    font-medium rounded-lg
    transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${isMobile ? 'min-h-[44px] min-w-[44px]' : 'min-h-[36px]'}
  `;

  const sizeClasses = {
    sm: isMobile ? 'px-4 py-3 text-sm' : 'px-3 py-2 text-sm',
    md: isMobile ? 'px-6 py-3 text-base' : 'px-4 py-2 text-sm',
    lg: isMobile ? 'px-8 py-4 text-lg' : 'px-6 py-3 text-base',
  };

  const variantClasses = {
    primary: theme === 'light'
      ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
      : 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-400',
    secondary: theme === 'light'
      ? 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500'
      : 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-400',
    danger: theme === 'light'
      ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
      : 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-400',
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </button>
  );
};

interface SwipeableCardProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  className?: string;
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  className = ''
}) => {
  const [startX, setStartX] = useState<number | null>(null);
  const [currentX, setCurrentX] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!startX || !isDragging) return;
    setCurrentX(e.touches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!startX || !currentX || !isDragging) {
      setStartX(null);
      setCurrentX(null);
      setIsDragging(false);
      return;
    }

    const diffX = currentX - startX;
    const threshold = 100; // Minimum swipe distance

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (diffX < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    }

    setStartX(null);
    setCurrentX(null);
    setIsDragging(false);
  };

  const translateX = isDragging && startX && currentX ? currentX - startX : 0;

  return (
    <div
      ref={cardRef}
      className={`
        ${className}
        transition-transform duration-200 ease-out
        ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}
        rounded-lg shadow-md
      `}
      style={{
        transform: `translateX(${translateX}px)`,
      }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {children}
    </div>
  );
};

export default {
  MobileTable,
  TouchFriendlyButton,
  SwipeableCard,
};
