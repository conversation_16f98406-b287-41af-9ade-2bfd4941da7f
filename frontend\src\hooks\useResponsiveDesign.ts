/**
 * Responsive Design Hooks
 * Provides utilities for responsive design and mobile optimization
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';

// Breakpoint definitions
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

export interface ViewportSize {
  width: number;
  height: number;
}

export interface ResponsiveState {
  viewport: ViewportSize;
  breakpoint: Breakpoint;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  touchDevice: boolean;
}

/**
 * Hook for responsive viewport information
 */
export const useViewport = (): ResponsiveState => {
  const [viewport, setViewport] = useState<ViewportSize>(() => ({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  }));

  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>(() => 
    typeof window !== 'undefined' 
      ? window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
      : 'landscape'
  );

  const [pixelRatio, setPixelRatio] = useState(() => 
    typeof window !== 'undefined' ? window.devicePixelRatio : 1
  );

  const [touchDevice, setTouchDevice] = useState(() => 
    typeof window !== 'undefined' ? 'ontouchstart' in window : false
  );

  const updateViewport = useCallback(
    debounce(() => {
      if (typeof window !== 'undefined') {
        const newWidth = window.innerWidth;
        const newHeight = window.innerHeight;
        
        setViewport({ width: newWidth, height: newHeight });
        setOrientation(newWidth > newHeight ? 'landscape' : 'portrait');
        setPixelRatio(window.devicePixelRatio);
      }
    }, 100),
    []
  );

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initial check for touch device
    setTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);

    window.addEventListener('resize', updateViewport);
    window.addEventListener('orientationchange', updateViewport);

    return () => {
      window.removeEventListener('resize', updateViewport);
      window.removeEventListener('orientationchange', updateViewport);
      updateViewport.cancel();
    };
  }, [updateViewport]);

  const breakpoint = useMemo((): Breakpoint => {
    const width = viewport.width;
    
    if (width >= BREAKPOINTS['2xl']) return '2xl';
    if (width >= BREAKPOINTS.xl) return 'xl';
    if (width >= BREAKPOINTS.lg) return 'lg';
    if (width >= BREAKPOINTS.md) return 'md';
    if (width >= BREAKPOINTS.sm) return 'sm';
    return 'xs';
  }, [viewport.width]);

  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';
  const isTablet = breakpoint === 'md';
  const isDesktop = breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl';

  return {
    viewport,
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
    orientation,
    pixelRatio,
    touchDevice,
  };
};

/**
 * Hook for responsive values based on breakpoints
 */
export const useResponsiveValue = <T>(values: Partial<Record<Breakpoint, T>>): T | undefined => {
  const { breakpoint } = useViewport();
  
  return useMemo(() => {
    // Find the best matching value for current breakpoint
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    // Look for exact match first
    if (values[breakpoint] !== undefined) {
      return values[breakpoint];
    }
    
    // Look for smaller breakpoints
    for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp];
      }
    }
    
    // Look for larger breakpoints
    for (let i = currentIndex - 1; i >= 0; i--) {
      const bp = breakpointOrder[i];
      if (values[bp] !== undefined) {
        return values[bp];
      }
    }
    
    return undefined;
  }, [breakpoint, values]);
};

/**
 * Hook for responsive grid columns
 */
export const useResponsiveColumns = (
  columns: Partial<Record<Breakpoint, number>> = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    '2xl': 6
  }
): number => {
  return useResponsiveValue(columns) || 1;
};

/**
 * Hook for responsive table display
 */
export const useResponsiveTable = () => {
  const { isMobile, isTablet } = useViewport();
  
  return useMemo(() => ({
    showAsCards: isMobile,
    showScrollableTable: isTablet,
    showFullTable: !isMobile && !isTablet,
    maxVisibleColumns: isMobile ? 2 : isTablet ? 4 : 8,
  }), [isMobile, isTablet]);
};

/**
 * Hook for responsive chart dimensions
 */
export const useResponsiveChart = () => {
  const { viewport, isMobile, isTablet } = useViewport();
  
  return useMemo(() => {
    const baseWidth = viewport.width - 32; // Account for padding
    const baseHeight = isMobile ? 250 : isTablet ? 300 : 400;
    
    return {
      width: Math.min(baseWidth, isMobile ? 350 : isTablet ? 600 : 800),
      height: baseHeight,
      margin: {
        top: isMobile ? 10 : 20,
        right: isMobile ? 10 : 30,
        bottom: isMobile ? 30 : 50,
        left: isMobile ? 30 : 60,
      },
      fontSize: isMobile ? 10 : isTablet ? 12 : 14,
    };
  }, [viewport, isMobile, isTablet]);
};

/**
 * Hook for responsive navigation
 */
export const useResponsiveNavigation = () => {
  const { isMobile, isTablet } = useViewport();
  
  return useMemo(() => ({
    showMobileMenu: isMobile,
    showTabletMenu: isTablet,
    showDesktopMenu: !isMobile && !isTablet,
    collapseSidebar: isMobile || isTablet,
    showBottomNavigation: isMobile,
  }), [isMobile, isTablet]);
};

/**
 * Hook for responsive modal sizing
 */
export const useResponsiveModal = () => {
  const { viewport, isMobile, isTablet } = useViewport();
  
  return useMemo(() => {
    if (isMobile) {
      return {
        width: '100%',
        height: '100%',
        maxWidth: 'none',
        maxHeight: 'none',
        margin: 0,
        borderRadius: 0,
      };
    }
    
    if (isTablet) {
      return {
        width: '90%',
        height: '90%',
        maxWidth: '600px',
        maxHeight: '80vh',
        margin: 'auto',
        borderRadius: '8px',
      };
    }
    
    return {
      width: 'auto',
      height: 'auto',
      maxWidth: '800px',
      maxHeight: '90vh',
      margin: 'auto',
      borderRadius: '12px',
    };
  }, [viewport, isMobile, isTablet]);
};

/**
 * Hook for responsive font sizes
 */
export const useResponsiveFontSize = () => {
  const { breakpoint } = useViewport();
  
  return useMemo(() => {
    const sizes = {
      xs: {
        xs: '0.75rem',    // 12px
        sm: '0.875rem',   // 14px
        base: '1rem',     // 16px
        lg: '1.125rem',   // 18px
        xl: '1.25rem',    // 20px
        '2xl': '1.5rem',  // 24px
        '3xl': '1.875rem', // 30px
        '4xl': '2.25rem', // 36px
      },
      sm: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
      },
      md: {
        xs: '0.875rem',
        sm: '1rem',
        base: '1.125rem',
        lg: '1.25rem',
        xl: '1.5rem',
        '2xl': '1.875rem',
        '3xl': '2.25rem',
        '4xl': '3rem',
      },
      lg: {
        xs: '0.875rem',
        sm: '1rem',
        base: '1.125rem',
        lg: '1.25rem',
        xl: '1.5rem',
        '2xl': '1.875rem',
        '3xl': '2.25rem',
        '4xl': '3rem',
      },
      xl: {
        xs: '0.875rem',
        sm: '1rem',
        base: '1.125rem',
        lg: '1.25rem',
        xl: '1.5rem',
        '2xl': '1.875rem',
        '3xl': '2.25rem',
        '4xl': '3rem',
      },
      '2xl': {
        xs: '0.875rem',
        sm: '1rem',
        base: '1.125rem',
        lg: '1.25rem',
        xl: '1.5rem',
        '2xl': '1.875rem',
        '3xl': '2.25rem',
        '4xl': '3rem',
      },
    };
    
    return sizes[breakpoint] || sizes.md;
  }, [breakpoint]);
};

/**
 * Hook for responsive spacing
 */
export const useResponsiveSpacing = () => {
  const { isMobile, isTablet } = useViewport();
  
  return useMemo(() => {
    if (isMobile) {
      return {
        xs: '0.25rem',  // 4px
        sm: '0.5rem',   // 8px
        md: '0.75rem',  // 12px
        lg: '1rem',     // 16px
        xl: '1.5rem',   // 24px
        '2xl': '2rem',  // 32px
        '3xl': '2.5rem', // 40px
      };
    }
    
    if (isTablet) {
      return {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '2.5rem',
        '3xl': '3rem',
      };
    }
    
    return {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem',
      '3xl': '4rem',
    };
  }, [isMobile, isTablet]);
};

/**
 * Hook for responsive performance optimization
 */
export const useResponsivePerformance = () => {
  const { isMobile, isTablet, touchDevice } = useViewport();
  
  return useMemo(() => ({
    // Reduce animations on mobile for better performance
    reduceAnimations: isMobile,
    
    // Use smaller images on mobile
    imageQuality: isMobile ? 'low' : isTablet ? 'medium' : 'high',
    
    // Reduce chart complexity on mobile
    chartComplexity: isMobile ? 'simple' : isTablet ? 'medium' : 'complex',
    
    // Lazy load more aggressively on mobile
    lazyLoadThreshold: isMobile ? 0.1 : 0.3,
    
    // Use touch-optimized interactions
    touchOptimized: touchDevice,
    
    // Reduce concurrent requests on mobile
    maxConcurrentRequests: isMobile ? 2 : isTablet ? 4 : 6,
  }), [isMobile, isTablet, touchDevice]);
};

/**
 * Utility function to get responsive class names
 */
export const getResponsiveClasses = (
  classes: Partial<Record<Breakpoint, string>>,
  currentBreakpoint: Breakpoint
): string => {
  const breakpointOrder: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  
  // Find the best matching class for current breakpoint
  for (let i = currentIndex; i >= 0; i--) {
    const bp = breakpointOrder[i];
    if (classes[bp]) {
      return classes[bp]!;
    }
  }
  
  return '';
};
