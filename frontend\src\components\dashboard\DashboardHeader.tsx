import React, { memo } from 'react'; // Import memo
import { useTheme } from '../../contexts/ThemeContext';

interface DashboardHeaderProps {
  lastUpdated: string;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ lastUpdated }) => {
  const { theme } = useTheme();
  return (
    <header className="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center">
      <h1 className={`text-4xl font-bold mb-4 md:mb-0 ${theme === 'light' ? 'text-indigo-700' : 'text-indigo-400'}`}>
        Asahikawa Performance Hub
      </h1>
      <div className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
        Last updated: {lastUpdated}
      </div>
    </header>
  );
};

export default memo(DashboardHeader); // Wrap with memo
