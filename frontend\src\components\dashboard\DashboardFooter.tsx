import React, { memo } from 'react'; // Import memo
import { useTheme } from '../../contexts/ThemeContext';

const DashboardFooter: React.FC = () => {
  const { theme } = useTheme();
  return (
    <footer className={`mt-16 pt-8 border-t ${theme === 'light' ? 'border-gray-200 text-gray-600' : 'border-gray-700 text-gray-400'} text-center text-sm`}>
      <p>&copy; {new Date().getFullYear()} Asahikawa Performance Hub. All rights reserved.</p>
      <p>Designed for peak performance analysis.</p>
    </footer>
  );
};

export default memo(DashboardFooter); // Wrap with memo
