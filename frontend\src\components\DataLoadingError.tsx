import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface DataLoadingErrorProps {
  message?: string;
  details?: string;
}

const DataLoadingError: React.FC<DataLoadingErrorProps> = ({ 
  message = "There was a problem loading the data.",
  details = "Please check the browser console for more information."
}) => {
  const { theme } = useTheme();
  
  return (
    <div className="flex justify-center items-center py-12">
      <div className={`p-6 rounded-lg shadow-md max-w-lg ${theme === 'light' ? 'bg-red-50 text-red-800' : 'bg-red-900/20 text-red-300'}`}>
        <h3 className="text-xl font-bold mb-4">Data Loading Error</h3>
        <p className="mb-4">{message}</p>
        <p className="text-sm opacity-80">{details}</p>
        <div className="mt-6">
          <button 
            onClick={() => window.location.reload()}
            className={`px-4 py-2 rounded-md ${theme === 'light' ? 'bg-red-600 text-white hover:bg-red-700' : 'bg-red-700 text-white hover:bg-red-600'} transition-colors`}
          >
            Reload Page
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataLoadingError;
