"""
Data Validation Module
Provides comprehensive validation for API inputs, file uploads, and data integrity.
"""

import re
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, validator, ValidationError
from fastapi import HTTPException
import logging

logger = logging.getLogger(__name__)

# Validation constants
VALID_SCAN_EXTENSIONS = ['.csv', '.xlsx', '.xls']
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
MIN_PLAYERS_PER_SCAN = 10
MAX_PLAYERS_PER_SCAN = 10000
VALID_KVK_STATUSES = ['upcoming', 'active', 'completed']
VALID_PHASES = ['Kingdom vs Kingdom', 'Preparation', 'Battle Phase', 'Cleanup']

# Required columns for scan files
REQUIRED_COLUMNS = [
    'governor_id',
    'player_name', 
    'power',
    'total_kill_points',
    'dead_troops'
]

OPTIONAL_COLUMNS = [
    'alliance',
    'kill_points_t1',
    'kill_points_t2', 
    'kill_points_t3',
    'kill_points_t4',
    'kill_points_t5',
    'ranged_points'
]


class ValidationError(Exception):
    """Custom validation error with detailed information."""
    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.field = field
        self.details = details or {}
        super().__init__(self.message)


class ValidationResult(BaseModel):
    """Result of validation operation."""
    is_valid: bool
    errors: List[Dict[str, Any]] = []
    warnings: List[Dict[str, Any]] = []
    data: Optional[Any] = None
    
    def add_error(self, message: str, field: str = None, details: Dict[str, Any] = None):
        """Add an error to the validation result."""
        self.errors.append({
            'message': message,
            'field': field,
            'details': details or {}
        })
        self.is_valid = False
    
    def add_warning(self, message: str, field: str = None, details: Dict[str, Any] = None):
        """Add a warning to the validation result."""
        self.warnings.append({
            'message': message,
            'field': field,
            'details': details or {}
        })


class ScanDataValidator:
    """Validates scan file data and structure."""
    
    @staticmethod
    def validate_file_format(filename: str, file_size: int) -> ValidationResult:
        """Validate file format and size."""
        result = ValidationResult(is_valid=True)
        
        # Check file extension
        if not any(filename.lower().endswith(ext) for ext in VALID_SCAN_EXTENSIONS):
            result.add_error(
                f"Invalid file format. Supported formats: {', '.join(VALID_SCAN_EXTENSIONS)}",
                field="file_format",
                details={"filename": filename, "supported_formats": VALID_SCAN_EXTENSIONS}
            )
        
        # Check file size
        if file_size > MAX_FILE_SIZE:
            result.add_error(
                f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size ({MAX_FILE_SIZE / 1024 / 1024}MB)",
                field="file_size",
                details={"file_size": file_size, "max_size": MAX_FILE_SIZE}
            )
        
        return result
    
    @staticmethod
    def validate_dataframe_structure(df: pd.DataFrame) -> ValidationResult:
        """Validate DataFrame structure and required columns."""
        result = ValidationResult(is_valid=True)
        
        # Check if DataFrame is empty
        if df.empty:
            result.add_error("File contains no data", field="data_content")
            return result
        
        # Check minimum number of players
        if len(df) < MIN_PLAYERS_PER_SCAN:
            result.add_error(
                f"Insufficient player data. Minimum {MIN_PLAYERS_PER_SCAN} players required, found {len(df)}",
                field="player_count",
                details={"found": len(df), "minimum": MIN_PLAYERS_PER_SCAN}
            )
        
        # Check maximum number of players
        if len(df) > MAX_PLAYERS_PER_SCAN:
            result.add_error(
                f"Too many players. Maximum {MAX_PLAYERS_PER_SCAN} players allowed, found {len(df)}",
                field="player_count",
                details={"found": len(df), "maximum": MAX_PLAYERS_PER_SCAN}
            )
        
        # Check required columns
        missing_columns = [col for col in REQUIRED_COLUMNS if col not in df.columns]
        if missing_columns:
            result.add_error(
                f"Missing required columns: {', '.join(missing_columns)}",
                field="columns",
                details={"missing": missing_columns, "required": REQUIRED_COLUMNS}
            )
        
        # Check for duplicate governor IDs
        if 'governor_id' in df.columns:
            duplicates = df[df['governor_id'].duplicated()]
            if not duplicates.empty:
                result.add_error(
                    f"Duplicate governor IDs found: {duplicates['governor_id'].tolist()}",
                    field="governor_id",
                    details={"duplicates": duplicates['governor_id'].tolist()}
                )
        
        return result
    
    @staticmethod
    def validate_data_quality(df: pd.DataFrame) -> ValidationResult:
        """Validate data quality and detect anomalies."""
        result = ValidationResult(is_valid=True)
        
        # Check for null values in critical fields
        for col in REQUIRED_COLUMNS:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    result.add_warning(
                        f"Column '{col}' has {null_count} null values",
                        field=col,
                        details={"null_count": null_count, "total_rows": len(df)}
                    )
        
        # Validate governor ID format
        if 'governor_id' in df.columns:
            invalid_ids = df[~df['governor_id'].astype(str).str.match(r'^\d+$')]['governor_id']
            if not invalid_ids.empty:
                result.add_error(
                    f"Invalid governor ID format: {invalid_ids.tolist()}",
                    field="governor_id",
                    details={"invalid_ids": invalid_ids.tolist()}
                )
        
        # Validate numeric fields
        numeric_fields = ['power', 'total_kill_points', 'dead_troops']
        for field in numeric_fields:
            if field in df.columns:
                # Check for negative values
                negative_values = df[df[field] < 0]
                if not negative_values.empty:
                    result.add_warning(
                        f"Negative values found in '{field}': {len(negative_values)} rows",
                        field=field,
                        details={"negative_count": len(negative_values)}
                    )
                
                # Check for unrealistic values
                if field == 'power':
                    max_realistic_power = 1_000_000_000  # 1B power
                    unrealistic = df[df[field] > max_realistic_power]
                    if not unrealistic.empty:
                        result.add_warning(
                            f"Unrealistically high power values: {len(unrealistic)} players",
                            field=field,
                            details={"count": len(unrealistic), "threshold": max_realistic_power}
                        )
                
                elif field == 'total_kill_points':
                    max_realistic_kp = 10_000_000_000  # 10B KP
                    unrealistic = df[df[field] > max_realistic_kp]
                    if not unrealistic.empty:
                        result.add_warning(
                            f"Unrealistically high kill points: {len(unrealistic)} players",
                            field=field,
                            details={"count": len(unrealistic), "threshold": max_realistic_kp}
                        )
        
        return result


class KvKValidator:
    """Validates KvK data and operations."""
    
    @staticmethod
    def validate_kvk_data(name: str, start_date: datetime, end_date: Optional[datetime], 
                         status: str, season: int) -> ValidationResult:
        """Validate KvK creation/update data."""
        result = ValidationResult(is_valid=True)
        
        # Validate name
        if not name or len(name.strip()) < 3:
            result.add_error(
                "KvK name must be at least 3 characters long",
                field="name",
                details={"provided": name}
            )
        
        if len(name) > 100:
            result.add_error(
                "KvK name cannot exceed 100 characters",
                field="name",
                details={"length": len(name), "max": 100}
            )
        
        # Validate status
        if status not in VALID_KVK_STATUSES:
            result.add_error(
                f"Invalid KvK status. Must be one of: {', '.join(VALID_KVK_STATUSES)}",
                field="status",
                details={"provided": status, "valid": VALID_KVK_STATUSES}
            )
        
        # Validate dates
        if start_date > datetime.utcnow() + timedelta(days=365):
            result.add_error(
                "Start date cannot be more than 1 year in the future",
                field="start_date",
                details={"provided": start_date.isoformat()}
            )
        
        if end_date and end_date <= start_date:
            result.add_error(
                "End date must be after start date",
                field="end_date",
                details={"start_date": start_date.isoformat(), "end_date": end_date.isoformat()}
            )
        
        # Validate season
        if season < 1 or season > 100:
            result.add_error(
                "Season must be between 1 and 100",
                field="season",
                details={"provided": season, "min": 1, "max": 100}
            )
        
        return result


class APIValidator:
    """Validates API request parameters."""
    
    @staticmethod
    def validate_pagination(skip: int, limit: int) -> ValidationResult:
        """Validate pagination parameters."""
        result = ValidationResult(is_valid=True)
        
        if skip < 0:
            result.add_error(
                "Skip parameter cannot be negative",
                field="skip",
                details={"provided": skip}
            )
        
        if limit < 1:
            result.add_error(
                "Limit parameter must be at least 1",
                field="limit",
                details={"provided": limit}
            )
        
        if limit > 1000:
            result.add_error(
                "Limit parameter cannot exceed 1000",
                field="limit",
                details={"provided": limit, "max": 1000}
            )
        
        return result
    
    @staticmethod
    def validate_id_parameter(id_value: Union[int, str], field_name: str) -> ValidationResult:
        """Validate ID parameters."""
        result = ValidationResult(is_valid=True)
        
        try:
            id_int = int(id_value)
            if id_int <= 0:
                result.add_error(
                    f"{field_name} must be a positive integer",
                    field=field_name,
                    details={"provided": id_value}
                )
        except (ValueError, TypeError):
            result.add_error(
                f"{field_name} must be a valid integer",
                field=field_name,
                details={"provided": id_value, "type": type(id_value).__name__}
            )
        
        return result


def validate_request_data(data: Dict[str, Any], required_fields: List[str], 
                         optional_fields: List[str] = None) -> ValidationResult:
    """Generic request data validation."""
    result = ValidationResult(is_valid=True)
    optional_fields = optional_fields or []
    
    # Check required fields
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        result.add_error(
            f"Missing required fields: {', '.join(missing_fields)}",
            field="required_fields",
            details={"missing": missing_fields, "required": required_fields}
        )
    
    # Check for unexpected fields
    allowed_fields = set(required_fields + optional_fields)
    unexpected_fields = [field for field in data.keys() if field not in allowed_fields]
    if unexpected_fields:
        result.add_warning(
            f"Unexpected fields will be ignored: {', '.join(unexpected_fields)}",
            field="unexpected_fields",
            details={"unexpected": unexpected_fields, "allowed": list(allowed_fields)}
        )
    
    return result


def raise_validation_error(result: ValidationResult):
    """Convert ValidationResult to HTTPException."""
    if not result.is_valid:
        error_details = {
            "validation_errors": result.errors,
            "warnings": result.warnings
        }
        raise HTTPException(
            status_code=422,
            detail=f"Validation failed: {result.errors[0]['message'] if result.errors else 'Unknown error'}",
            headers={"X-Validation-Details": str(error_details)}
        )
