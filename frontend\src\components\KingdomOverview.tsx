import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Fa<PERSON><PERSON><PERSON>, FaShieldAlt, FaSkull<PERSON>rossbones, FaChartLine, FaCrown, FaTrophy, FaInfoCircle } from 'react-icons/fa';
import { formatLargeNumber } from '../utils/formatters';
import KvKPerformanceDashboard from './KvKPerformanceDashboard';

interface KingdomOverviewProps {
  kingdomData: any;
  performance_data?: any;
  has_baseline?: boolean;
}

const KingdomOverview: React.FC<KingdomOverviewProps> = ({ 
  kingdomData, 
  performance_data, 
  has_baseline = false 
}) => {
  const { theme } = useTheme();

  // Extract data with safe defaults
  const kingdom_stats = kingdomData?.kingdom_stats || {};
  const transformedPlayers = performance_data?.performance_data || [];

  return (
    <div className="space-y-8">
      <div className="space-y-6">
        {/* Kingdom Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className={theme === 'light' ? 'p-6 rounded-xl shadow-lg bg-white' : 'p-6 rounded-xl shadow-lg bg-gray-800'}>
            <div className="flex items-center">
              <FaUsers className={theme === 'light' ? 'text-3xl mr-4 text-blue-600' : 'text-3xl mr-4 text-blue-400'} />
              <div>
                <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>Total Players</p>
                <p className={theme === 'light' ? 'text-2xl font-bold text-gray-900' : 'text-2xl font-bold text-white'}>
                  {kingdom_stats.total_players || 0}
                </p>
              </div>
            </div>
          </div>

          <div className={theme === 'light' ? 'p-6 rounded-xl shadow-lg bg-white' : 'p-6 rounded-xl shadow-lg bg-gray-800'}>
            <div className="flex items-center">
              <FaShieldAlt className={theme === 'light' ? 'text-3xl mr-4 text-green-600' : 'text-3xl mr-4 text-green-400'} />
              <div>
                <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>Total Power</p>
                <p className={theme === 'light' ? 'text-2xl font-bold text-gray-900' : 'text-2xl font-bold text-white'}>
                  {formatLargeNumber(kingdom_stats.total_power || 0)}
                </p>
              </div>
            </div>
          </div>

          <div className={theme === 'light' ? 'p-6 rounded-xl shadow-lg bg-white' : 'p-6 rounded-xl shadow-lg bg-gray-800'}>
            <div className="flex items-center">
              <FaSkullCrossbones className={theme === 'light' ? 'text-3xl mr-4 text-red-600' : 'text-3xl mr-4 text-red-400'} />
              <div>
                <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>Total Kill Points</p>
                <p className={theme === 'light' ? 'text-2xl font-bold text-gray-900' : 'text-2xl font-bold text-white'}>
                  {formatLargeNumber(kingdom_stats.total_kill_points || 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Last 7 Days Gains Section */}
        {kingdomData.last_7_days_gains && (
          <div className="mb-8">
            <h2 className={theme === 'light' ? 'text-2xl font-bold mb-4 text-gray-900' : 'text-2xl font-bold mb-4 text-white'}>
              Last 7 Days Gains
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={theme === 'light' ? 'p-4 rounded-xl bg-white bg-opacity-70 backdrop-blur-sm' : 'p-4 rounded-xl bg-gray-800 bg-opacity-70 backdrop-blur-sm'}>
                <div className="flex items-center">
                  <FaChartLine className={theme === 'light' ? 'text-2xl mr-3 text-green-600' : 'text-2xl mr-3 text-green-400'} />
                  <div>
                    <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>KP Gain</p>
                    <p className={theme === 'light' ? 'text-xl font-bold text-gray-900' : 'text-xl font-bold text-white'}>
                      {formatLargeNumber(kingdomData.last_7_days_gains.total_kp_gain)}
                    </p>
                  </div>
                </div>
              </div>
              <div className={theme === 'light' ? 'p-4 rounded-xl bg-white bg-opacity-70 backdrop-blur-sm' : 'p-4 rounded-xl bg-gray-800 bg-opacity-70 backdrop-blur-sm'}>
                <div className="flex items-center">
                  <FaShieldAlt className={theme === 'light' ? 'text-2xl mr-3 text-purple-600' : 'text-2xl mr-3 text-purple-400'} />
                  <div>
                    <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>Power Gain</p>
                    <p className={theme === 'light' ? 'text-xl font-bold text-gray-900' : 'text-xl font-bold text-white'}>
                      {formatLargeNumber(kingdomData.last_7_days_gains.total_power_gain)}
                    </p>
                  </div>
                </div>
              </div>
              <div className={theme === 'light' ? 'p-4 rounded-xl bg-white bg-opacity-70 backdrop-blur-sm' : 'p-4 rounded-xl bg-gray-800 bg-opacity-70 backdrop-blur-sm'}>
                <div className="flex items-center">
                  <FaSkullCrossbones className={theme === 'light' ? 'text-2xl mr-3 text-red-600' : 'text-2xl mr-3 text-red-400'} />
                  <div>
                    <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>Dead Troops</p>
                    <p className={theme === 'light' ? 'text-xl font-bold text-gray-900' : 'text-xl font-bold text-white'}>
                      {formatLargeNumber(kingdomData.last_7_days_gains.total_dead_troops)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Champions Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Power Champion */}
          <div className={theme === 'light' ? 'p-6 rounded-xl shadow-lg bg-white' : 'p-6 rounded-xl shadow-lg bg-gray-800'}>
            <div className="flex items-center mb-4">
              <FaCrown className={theme === 'light' ? 'text-2xl mr-3 text-yellow-500' : 'text-2xl mr-3 text-yellow-400'} />
              <h3 className={theme === 'light' ? 'text-lg font-semibold text-gray-800' : 'text-lg font-semibold text-gray-100'}>
                Power Champion
              </h3>
            </div>
            {kingdom_stats.power_champion ? (
              <div>
                <p className={theme === 'light' ? 'text-2xl font-bold text-gray-900' : 'text-2xl font-bold text-white'}>
                  {kingdom_stats.power_champion.player_name}
                </p>
                <p className={theme === 'light' ? 'text-lg text-blue-600' : 'text-lg text-blue-400'}>
                  {formatLargeNumber(kingdom_stats.power_champion.power)} Power
                </p>
                {kingdom_stats.power_champion.alliance && (
                  <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>
                    {kingdom_stats.power_champion.alliance}
                  </p>
                )}
              </div>
            ) : (
              <p className={theme === 'light' ? 'text-gray-500' : 'text-gray-400'}>No data available</p>
            )}
          </div>

          {/* KP Champion */}
          <div className={theme === 'light' ? 'p-6 rounded-xl shadow-lg bg-white' : 'p-6 rounded-xl shadow-lg bg-gray-800'}>
            <div className="flex items-center mb-4">
              <FaTrophy className={theme === 'light' ? 'text-2xl mr-3 text-red-500' : 'text-2xl mr-3 text-red-400'} />
              <h3 className={theme === 'light' ? 'text-lg font-semibold text-gray-800' : 'text-lg font-semibold text-gray-100'}>
                Kill Points Champion
              </h3>
            </div>
            {kingdom_stats.kp_champion ? (
              <div>
                <p className={theme === 'light' ? 'text-2xl font-bold text-gray-900' : 'text-2xl font-bold text-white'}>
                  {kingdom_stats.kp_champion.player_name}
                </p>
                <p className={theme === 'light' ? 'text-lg text-red-600' : 'text-lg text-red-400'}>
                  {formatLargeNumber(kingdom_stats.kp_champion.kill_points)} KP
                </p>
                {kingdom_stats.kp_champion.alliance && (
                  <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>
                    {kingdom_stats.kp_champion.alliance}
                  </p>
                )}
              </div>
            ) : (
              <p className={theme === 'light' ? 'text-gray-500' : 'text-gray-400'}>No data available</p>
            )}
          </div>
        </div>

        {/* Performance Dashboard */}
        {has_baseline && performance_data && transformedPlayers.length > 0 ? (
          <div>
            <div className="mb-6">
              <h2 className={theme === 'light' ? 'text-2xl font-bold text-gray-900' : 'text-2xl font-bold text-white'}>
                Kingdom Performance Analysis
              </h2>
              <p className={theme === 'light' ? 'text-sm text-gray-600' : 'text-sm text-gray-400'}>
                Player gains and performance metrics since baseline scan
              </p>
            </div>
            <KvKPerformanceDashboard
              players={transformedPlayers}
              summaryStats={performance_data?.summary_stats}
              isBaselineOnly={false}
            />
          </div>
        ) : (
          <div className={theme === 'light' ? 'p-8 rounded-xl text-center bg-white' : 'p-8 rounded-xl text-center bg-gray-800'}>
            <FaInfoCircle className={theme === 'light' ? 'mx-auto h-12 w-12 mb-4 text-blue-500' : 'mx-auto h-12 w-12 mb-4 text-blue-400'} />
            <h3 className={theme === 'light' ? 'text-xl font-semibold mb-2 text-gray-800' : 'text-xl font-semibold mb-2 text-gray-100'}>
              Performance Analysis Unavailable
            </h3>
            <p className={theme === 'light' ? 'text-gray-600' : 'text-gray-400'}>
              {!has_baseline
                ? "A baseline scan is required to show performance analysis. Upload a scan and mark it as baseline to enable performance tracking."
                : "No performance data available. Upload additional scans to track player progress."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default KingdomOverview;
