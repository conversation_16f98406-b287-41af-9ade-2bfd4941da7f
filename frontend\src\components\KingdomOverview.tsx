import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useTheme } from '../contexts/ThemeContext';
import { getKingdomOverview } from '../api/api';
import { formatLargeNumber, formatDate, timeAgo } from '../utils/formatters';
import KvKPerformanceDashboard from './KvKPerformanceDashboard';
import { PlayerScanData } from '../types/dataTypes';
import {
  FaCrown,
  FaTrophy,
  FaUsers,
  FaShieldAlt,
  FaFire,
  FaSkullCrossbones,
  FaChartLine,
  FaExclamationTriangle,
  FaInfoCircle,
  FaCalendarAlt
} from 'react-icons/fa';

const KingdomOverview: React.FC = () => {
  const { theme } = useTheme();

  const { data: kingdomData, isLoading, error } = useQuery({
    queryKey: ['kingdomOverview'],
    queryFn: getKingdomOverview,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`flex flex-col items-center justify-center min-h-[400px] ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
          <p className="text-lg">Loading kingdom overview...</p>
        </div>
      </div>
    );
  }

  if (error || !kingdomData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`text-center ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
          <FaExclamationTriangle className="mx-auto h-12 w-12 mb-4" />
          <h3 className="text-xl font-semibold mb-2">Failed to Load Kingdom Data</h3>
          <p>Unable to fetch kingdom overview. Please try again later.</p>
        </div>
      </div>
    );
  }

  const { kingdom_stats, performance_data, has_baseline } = kingdomData;

  // Transform performance data for the dashboard component
  const transformedPlayers: PlayerScanData[] = performance_data?.performance_data?.map((player: any) => ({
    governorId: player.governor_id || '',
    name: player.player_name || player.name || '',
    alliance: player.alliance || '',
    power: player.power_delta || 0,
    killPoints: player.kp_delta || 0,
    deads: player.dead_delta || 0,
    t1Kills: 0,
    t2Kills: 0,
    t3Kills: 0,
    t4Kills: player.t4_delta || 0,
    t5Kills: player.t5_delta || 0,
    totalKills: 0,
    t45Kills: player.t45_delta || 0,
    ranged: 0,
    rssGathered: 0,
    rssAssisted: 0,
    helps: 0,
    statId: player.id,
    scanId: player.scan_id,
    timestamp: player.timestamp
  })) || [];

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-6">
              <div className={`p-4 rounded-xl mr-6 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaCrown className="text-3xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Kingdom 2358 Overview
                </h1>
                <p className={`text-xl mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Asahikawa • Live Kingdom Analytics
                </p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'
              }`}>
                <div className="flex items-center">
                  <FaUsers className={`text-2xl mr-3 ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>Players</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                      {kingdom_stats.total_players}
                    </p>
                  </div>
                </div>
              </div>

              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'
              }`}>
                <div className="flex items-center">
                  <FaFire className={`text-2xl mr-3 ${theme === 'light' ? 'text-orange-600' : 'text-orange-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>Total Power</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                      {formatLargeNumber(kingdom_stats.total_power)}
                    </p>
                  </div>
                </div>
              </div>

              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'
              }`}>
                <div className="flex items-center">
                  <FaTrophy className={`text-2xl mr-3 ${theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>Total KP</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                      {formatLargeNumber(kingdom_stats.total_kill_points)}
                    </p>
                  </div>
                </div>
              </div>

              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'
              }`}>
                <div className="flex items-center">
                  <FaCalendarAlt className={`text-2xl mr-3 ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>Last Update</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                      {kingdom_stats.scan_date ? timeAgo(kingdom_stats.scan_date) : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Last 7 Days Gains Section */}
        {kingdomData.last_7_days_gains && (
          <div className="mb-8">
            <h2 className={`text-2xl font-bold mb-4 ${theme === 'light' ? 'text-gray-900' : 'text-white'}">
              Last 7 Days Gains
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`p-4 rounded-xl ${theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'}">
                <div className="flex items-center">
                  <FaChartLine className={`text-2xl mr-3 ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>KP Gain</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}">
                      {formatLargeNumber(kingdomData.last_7_days_gains.total_kp_gain)}
                    </p>
                  </div>
                </div>
              </div>
              <div className={`p-4 rounded-xl ${theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'}">
                <div className="flex items-center">
                  <FaShieldAlt className={`text-2xl mr-3 ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>Power Gain</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}">
                      {formatLargeNumber(kingdomData.last_7_days_gains.total_power_gain)}
                    </p>
                  </div>
                </div>
              </div>
              <div className={`p-4 rounded-xl ${theme === 'light' ? 'bg-white/70 backdrop-blur-sm' : 'bg-gray-800/70 backdrop-blur-sm'}">
                <div className="flex items-center">
                  <FaSkullCrossbones className={`text-2xl mr-3 ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`} />
                  <div>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>Dead Troops</p>
                    <p className={`text-xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}">
                      {formatLargeNumber(kingdomData.last_7_days_gains.total_dead_troops)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Champions Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Power Champion */}
          <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <div className="flex items-center mb-4">
              <FaCrown className={`text-2xl mr-3 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400'}`} />
              <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
                Power Champion
              </h3>
            </div>
            {kingdom_stats.power_champion ? (
              <div>
                <p className={`text-2xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {kingdom_stats.power_champion.player_name}
                </p>
                <p className={`text-lg ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
                  {formatLargeNumber(kingdom_stats.power_champion.power)} Power
                </p>
                {kingdom_stats.power_champion.alliance && (
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                    {kingdom_stats.power_champion.alliance}
                  </p>
                )}
              </div>
            ) : (
              <p className={`${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>No data available</p>
            )}
          </div>

          {/* KP Champion */}
          <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <div className="flex items-center mb-4">
              <FaTrophy className={`text-2xl mr-3 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} />
              <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
                Kill Points Champion
              </h3>
            </div>
            {kingdom_stats.kp_champion ? (
              <div>
                <p className={`text-2xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {kingdom_stats.kp_champion.player_name}
                </p>
                <p className={`text-lg ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
                  {formatLargeNumber(kingdom_stats.kp_champion.kill_points)} KP
                </p>
                {kingdom_stats.kp_champion.alliance && (
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                    {kingdom_stats.kp_champion.alliance}
                  </p>
                )}
              </div>
            ) : (
              <p className={`${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>No data available</p>
            )}
          </div>
        </div>

        {/* Performance Dashboard */}
        {has_baseline && performance_data && transformedPlayers.length > 0 ? (
          <div>
            <div className="mb-6">
              <h2 className={`text-2xl font-bold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                Kingdom Performance Analysis
              </h2>
              <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                Player gains and performance metrics since baseline scan
              </p>
            </div>
            <KvKPerformanceDashboard
              players={transformedPlayers}
              summaryStats={performance_data?.summary_stats}
              isBaselineOnly={false}
            />
          </div>
        ) : (
          <div className={`p-8 rounded-xl text-center ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <FaInfoCircle className={`mx-auto h-12 w-12 mb-4 ${theme === 'light' ? 'text-blue-500' : 'text-blue-400'}`} />
            <h3 className={`text-xl font-semibold mb-2 ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
              Performance Analysis Unavailable
            </h3>
            <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              {!has_baseline
                ? "A baseline scan is required to show performance analysis. Upload a scan and mark it as baseline to enable performance tracking."
                : "No performance data available. Upload additional scans to track player progress."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default KingdomOverview;
