/**
 * Consistent Loading States Component
 * Provides standardized loading indicators across the application
 */

import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { FaSpinner, FaChartLine, FaUsers, FaDatabase, FaCloudUploadAlt } from 'react-icons/fa';

interface LoadingStateProps {
  type?: 'default' | 'dashboard' | 'performance' | 'upload' | 'data';
  message?: string;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  progress?: number;
}

const LoadingStates: React.FC<LoadingStateProps> = ({
  type = 'default',
  message,
  size = 'medium',
  showProgress = false,
  progress = 0
}) => {
  const { theme } = useTheme();

  const getIcon = () => {
    switch (type) {
      case 'dashboard':
        return <FaChartLine className="animate-pulse" />;
      case 'performance':
        return <FaUsers className="animate-pulse" />;
      case 'upload':
        return <FaCloudUploadAlt className="animate-pulse" />;
      case 'data':
        return <FaDatabase className="animate-pulse" />;
      default:
        return <FaSpinner className="animate-spin" />;
    }
  };

  const getMessage = () => {
    if (message) return message;
    
    switch (type) {
      case 'dashboard':
        return 'Loading dashboard data...';
      case 'performance':
        return 'Calculating performance metrics...';
      case 'upload':
        return 'Processing file upload...';
      case 'data':
        return 'Fetching data...';
      default:
        return 'Loading...';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-lg p-4';
      case 'large':
        return 'text-4xl p-12';
      default:
        return 'text-2xl p-8';
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${getSizeClasses()} ${
      theme === 'light' ? 'text-gray-600' : 'text-gray-300'
    }`}>
      <div className={`mb-4 ${getSizeClasses()}`}>
        {getIcon()}
      </div>
      
      <p className="text-center font-medium mb-4">
        {getMessage()}
      </p>
      
      {showProgress && (
        <div className="w-full max-w-xs">
          <div className={`w-full bg-gray-200 rounded-full h-2 ${
            theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
          }`}>
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <p className="text-sm text-center mt-2">
            {Math.round(progress)}%
          </p>
        </div>
      )}
    </div>
  );
};

// Skeleton loading components for specific content types
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  const { theme } = useTheme();
  
  return (
    <div className="animate-pulse">
      {/* Header */}
      <div className="grid gap-4 mb-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, i) => (
          <div 
            key={i}
            className={`h-4 rounded ${
              theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
            }`}
          />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div 
          key={rowIndex}
          className="grid gap-4 mb-3" 
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div 
              key={colIndex}
              className={`h-3 rounded ${
                theme === 'light' ? 'bg-gray-200' : 'bg-gray-700'
              }`}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export const CardSkeleton: React.FC = () => {
  const { theme } = useTheme();
  
  return (
    <div className={`animate-pulse p-6 rounded-lg ${
      theme === 'light' ? 'bg-gray-100' : 'bg-gray-800'
    }`}>
      <div className={`h-4 rounded mb-4 ${
        theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
      }`} style={{ width: '60%' }} />
      
      <div className={`h-8 rounded mb-2 ${
        theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
      }`} style={{ width: '40%' }} />
      
      <div className={`h-3 rounded ${
        theme === 'light' ? 'bg-gray-200' : 'bg-gray-700'
      }`} style={{ width: '80%' }} />
    </div>
  );
};

export const ChartSkeleton: React.FC = () => {
  const { theme } = useTheme();
  
  return (
    <div className={`animate-pulse p-6 rounded-lg ${
      theme === 'light' ? 'bg-gray-100' : 'bg-gray-800'
    }`}>
      <div className={`h-4 rounded mb-6 ${
        theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
      }`} style={{ width: '50%' }} />
      
      <div className="flex items-end space-x-2 h-32">
        {Array.from({ length: 8 }).map((_, i) => (
          <div 
            key={i}
            className={`rounded-t ${
              theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
            }`}
            style={{ 
              width: '12%',
              height: `${Math.random() * 80 + 20}%`
            }}
          />
        ))}
      </div>
    </div>
  );
};

// Error state component
export const ErrorState: React.FC<{
  message?: string;
  onRetry?: () => void;
  type?: 'network' | 'data' | 'permission' | 'generic';
}> = ({ 
  message, 
  onRetry, 
  type = 'generic' 
}) => {
  const { theme } = useTheme();

  const getErrorMessage = () => {
    if (message) return message;
    
    switch (type) {
      case 'network':
        return 'Network connection error. Please check your internet connection.';
      case 'data':
        return 'Unable to load data. The data might be temporarily unavailable.';
      case 'permission':
        return 'You don\'t have permission to access this data.';
      default:
        return 'Something went wrong. Please try again.';
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${
      theme === 'light' ? 'text-gray-600' : 'text-gray-300'
    }`}>
      <div className="text-4xl mb-4 text-red-500">⚠️</div>
      
      <h3 className="text-lg font-semibold mb-2">
        Oops! Something went wrong
      </h3>
      
      <p className="mb-6 max-w-md">
        {getErrorMessage()}
      </p>
      
      {onRetry && (
        <button
          onClick={onRetry}
          className={`px-6 py-2 rounded-lg font-medium transition-colors ${
            theme === 'light'
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          Try Again
        </button>
      )}
    </div>
  );
};

// Empty state component
export const EmptyState: React.FC<{
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
  icon?: React.ReactNode;
}> = ({ 
  title = 'No data available',
  message = 'There\'s no data to display at the moment.',
  actionLabel,
  onAction,
  icon
}) => {
  const { theme } = useTheme();

  return (
    <div className={`flex flex-col items-center justify-center p-12 text-center ${
      theme === 'light' ? 'text-gray-500' : 'text-gray-400'
    }`}>
      <div className="text-6xl mb-6">
        {icon || '📊'}
      </div>
      
      <h3 className="text-xl font-semibold mb-2">
        {title}
      </h3>
      
      <p className="mb-6 max-w-md">
        {message}
      </p>
      
      {actionLabel && onAction && (
        <button
          onClick={onAction}
          className={`px-6 py-2 rounded-lg font-medium transition-colors ${
            theme === 'light'
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          {actionLabel}
        </button>
      )}
    </div>
  );
};

export default LoadingStates;
