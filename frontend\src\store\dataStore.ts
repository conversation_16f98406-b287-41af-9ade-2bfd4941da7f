/**
 * Centralized Data Store for Rise of Kingdoms Tracker
 * Manages application state and prevents data inconsistencies
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { KvKData, ScanData, PlayerScanData } from '../types/dataTypes';

// Define the store state interface
interface DataStoreState {
  // Dashboard Data
  dashboardData: any | null;
  dashboardLoading: boolean;
  dashboardError: string | null;
  dashboardLastUpdated: Date | null;

  // Performance Data
  performanceData: any | null;
  performance7DaysData: any | null;
  performanceLoading: boolean;
  performanceError: string | null;
  performanceLastUpdated: Date | null;

  // KvK Data
  kvkList: KvKData[];
  activeKvK: KvKData | null;
  kvkLoading: boolean;
  kvkError: string | null;
  kvkLastUpdated: Date | null;

  // Scan Data
  scanList: ScanData[];
  scanLoading: boolean;
  scanError: string | null;
  scanLastUpdated: Date | null;

  // Player Data
  playerData: PlayerScanData[];
  playerLoading: boolean;
  playerError: string | null;
  playerLastUpdated: Date | null;

  // Cache Management
  cacheExpiry: {
    dashboard: number;
    performance: number;
    kvk: number;
    scans: number;
    players: number;
  };

  // Actions
  setDashboardData: (data: any) => void;
  setDashboardLoading: (loading: boolean) => void;
  setDashboardError: (error: string | null) => void;

  setPerformanceData: (data: any) => void;
  setPerformance7DaysData: (data: any) => void;
  setPerformanceLoading: (loading: boolean) => void;
  setPerformanceError: (error: string | null) => void;

  setKvKList: (kvks: KvKData[]) => void;
  setActiveKvK: (kvk: KvKData | null) => void;
  setKvKLoading: (loading: boolean) => void;
  setKvKError: (error: string | null) => void;

  setScanList: (scans: ScanData[]) => void;
  setScanLoading: (loading: boolean) => void;
  setScanError: (error: string | null) => void;

  setPlayerData: (players: PlayerScanData[]) => void;
  setPlayerLoading: (loading: boolean) => void;
  setPlayerError: (error: string | null) => void;

  // Cache utilities
  isCacheValid: (type: keyof DataStoreState['cacheExpiry']) => boolean;
  invalidateCache: (type: keyof DataStoreState['cacheExpiry']) => void;
  clearAllData: () => void;
  refreshData: (type: 'dashboard' | 'performance' | 'kvk' | 'scans' | 'players' | 'all') => void;
}

// Cache expiry times (in milliseconds)
const CACHE_EXPIRY = {
  dashboard: 5 * 60 * 1000,    // 5 minutes
  performance: 10 * 60 * 1000, // 10 minutes
  kvk: 30 * 60 * 1000,         // 30 minutes
  scans: 60 * 60 * 1000,       // 1 hour
  players: 30 * 60 * 1000,     // 30 minutes
};

export const useDataStore = create<DataStoreState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        dashboardData: null,
        dashboardLoading: false,
        dashboardError: null,
        dashboardLastUpdated: null,

        performanceData: null,
        performance7DaysData: null,
        performanceLoading: false,
        performanceError: null,
        performanceLastUpdated: null,

        kvkList: [],
        activeKvK: null,
        kvkLoading: false,
        kvkError: null,
        kvkLastUpdated: null,

        scanList: [],
        scanLoading: false,
        scanError: null,
        scanLastUpdated: null,

        playerData: [],
        playerLoading: false,
        playerError: null,
        playerLastUpdated: null,

        cacheExpiry: {
          dashboard: 0,
          performance: 0,
          kvk: 0,
          scans: 0,
          players: 0,
        },

        // Dashboard actions
        setDashboardData: (data) => set((state) => ({
          dashboardData: data,
          dashboardError: null,
          dashboardLastUpdated: new Date(),
          cacheExpiry: {
            ...state.cacheExpiry,
            dashboard: Date.now() + CACHE_EXPIRY.dashboard
          }
        })),

        setDashboardLoading: (loading) => set({ dashboardLoading: loading }),
        setDashboardError: (error) => set({ dashboardError: error, dashboardLoading: false }),

        // Performance actions
        setPerformanceData: (data) => set((state) => ({
          performanceData: data,
          performanceError: null,
          performanceLastUpdated: new Date(),
          cacheExpiry: {
            ...state.cacheExpiry,
            performance: Date.now() + CACHE_EXPIRY.performance
          }
        })),

        setPerformance7DaysData: (data) => set({ performance7DaysData: data }),
        setPerformanceLoading: (loading) => set({ performanceLoading: loading }),
        setPerformanceError: (error) => set({ performanceError: error, performanceLoading: false }),

        // KvK actions
        setKvKList: (kvks) => set((state) => ({
          kvkList: kvks,
          kvkError: null,
          kvkLastUpdated: new Date(),
          cacheExpiry: {
            ...state.cacheExpiry,
            kvk: Date.now() + CACHE_EXPIRY.kvk
          }
        })),

        setActiveKvK: (kvk) => set({ activeKvK: kvk }),
        setKvKLoading: (loading) => set({ kvkLoading: loading }),
        setKvKError: (error) => set({ kvkError: error, kvkLoading: false }),

        // Scan actions
        setScanList: (scans) => set((state) => ({
          scanList: scans,
          scanError: null,
          scanLastUpdated: new Date(),
          cacheExpiry: {
            ...state.cacheExpiry,
            scans: Date.now() + CACHE_EXPIRY.scans
          }
        })),

        setScanLoading: (loading) => set({ scanLoading: loading }),
        setScanError: (error) => set({ scanError: error, scanLoading: false }),

        // Player actions
        setPlayerData: (players) => set((state) => ({
          playerData: players,
          playerError: null,
          playerLastUpdated: new Date(),
          cacheExpiry: {
            ...state.cacheExpiry,
            players: Date.now() + CACHE_EXPIRY.players
          }
        })),

        setPlayerLoading: (loading) => set({ playerLoading: loading }),
        setPlayerError: (error) => set({ playerError: error, playerLoading: false }),

        // Cache utilities
        isCacheValid: (type) => {
          const state = get();
          return Date.now() < state.cacheExpiry[type];
        },

        invalidateCache: (type) => set((state) => ({
          cacheExpiry: {
            ...state.cacheExpiry,
            [type]: 0
          }
        })),

        clearAllData: () => set({
          dashboardData: null,
          performanceData: null,
          performance7DaysData: null,
          kvkList: [],
          activeKvK: null,
          scanList: [],
          playerData: [],
          cacheExpiry: {
            dashboard: 0,
            performance: 0,
            kvk: 0,
            scans: 0,
            players: 0,
          }
        }),

        refreshData: (type) => {
          const state = get();
          if (type === 'all') {
            state.clearAllData();
          } else {
            state.invalidateCache(type);
          }
        },
      }),
      {
        name: 'rok-data-store',
        partialize: (state) => ({
          // Only persist non-loading states and cache expiry
          dashboardData: state.dashboardData,
          performanceData: state.performanceData,
          performance7DaysData: state.performance7DaysData,
          kvkList: state.kvkList,
          activeKvK: state.activeKvK,
          scanList: state.scanList,
          playerData: state.playerData,
          cacheExpiry: state.cacheExpiry,
        }),
      }
    ),
    {
      name: 'rok-data-store',
    }
  )
);

// Selector hooks for better performance
export const useDashboardData = () => useDataStore((state) => ({
  data: state.dashboardData,
  loading: state.dashboardLoading,
  error: state.dashboardError,
  lastUpdated: state.dashboardLastUpdated,
  isValid: state.isCacheValid('dashboard'),
}));

export const usePerformanceData = () => useDataStore((state) => ({
  data: state.performanceData,
  data7Days: state.performance7DaysData,
  loading: state.performanceLoading,
  error: state.performanceError,
  lastUpdated: state.performanceLastUpdated,
  isValid: state.isCacheValid('performance'),
}));

export const useKvKData = () => useDataStore((state) => ({
  list: state.kvkList,
  active: state.activeKvK,
  loading: state.kvkLoading,
  error: state.kvkError,
  lastUpdated: state.kvkLastUpdated,
  isValid: state.isCacheValid('kvk'),
}));
