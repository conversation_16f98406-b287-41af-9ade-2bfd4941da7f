import { KvKData, KvKStatus } from '../types/dataTypes';
import axios from '../config/axios';

/**
 * Fetch all KvK data
 * @returns Promise<KvKData[]> Array of KvK data
 */
export const getKvKList = async (): Promise<KvKData[]> => {
  try {
    const response = await axios.get('/api/kvks/');
    return response.data.map((kvk: any) => ({
      id: kvk.id.toString(),
      name: kvk.name,
      startDate: kvk.start_date,
      endDate: kvk.end_date,
      status: kvk.status as KvKStatus,
      season: kvk.season,
      createdAt: kvk.created_at,
      updatedAt: kvk.updated_at
    }));
  } catch (error) {
    console.error('Error fetching KvK list:', error);
    throw error;
  }
};

/**
 * Fetch a specific KvK by ID
 * @param kvkId - The ID of the KvK to fetch
 * @returns Promise<KvKData | null> The KvK data or null if not found
 */
export const getKvKById = async (kvkId: string): Promise<KvKData | null> => {
  try {
    const response = await axios.get(`/api/kvks/${kvkId}`);
    const kvk = response.data;
    return {
      id: kvk.id.toString(),
      name: kvk.name,
      startDate: kvk.start_date,
      endDate: kvk.end_date,
      status: kvk.status as KvKStatus,
      season: kvk.season,
      createdAt: kvk.created_at,
      updatedAt: kvk.updated_at
    };
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null;
    }
    console.error('Error fetching KvK by ID:', error);
    throw error;
  }
};

/**
 * Get active KvK
 * @returns Promise<KvKData | null> The active KvK or null if none found
 */
export const getActiveKvK = async (): Promise<KvKData | null> => {
  try {
    const response = await axios.get('/api/kvks/active');
    const kvk = response.data;
    return {
      id: kvk.id.toString(),
      name: kvk.name,
      startDate: kvk.start_date,
      endDate: kvk.end_date,
      status: kvk.status as KvKStatus,
      season: kvk.season,
      createdAt: kvk.created_at,
      updatedAt: kvk.updated_at
    };
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null;
    }
    console.error('Error fetching active KvK:', error);
    throw error;
  }
};

/**
 * Create a new KvK
 * @param name - Name of the KvK
 * @param startDate - Start date of the KvK
 * @param season - Season number
 * @returns Promise<KvKData> The created KvK data
 */
export const createKvK = async (name: string, startDate: string, season: number): Promise<KvKData> => {
  try {
    const response = await axios.post('/api/kvks/', {
      name,
      start_date: startDate,
      season,
      status: 'upcoming'
    });
    const kvk = response.data;
    return {
      id: kvk.id.toString(),
      name: kvk.name,
      startDate: kvk.start_date,
      endDate: kvk.end_date,
      status: kvk.status as KvKStatus,
      season: kvk.season,
      createdAt: kvk.created_at,
      updatedAt: kvk.updated_at
    };
  } catch (error) {
    console.error('Error creating KvK:', error);
    throw error;
  }
};

/**
 * Update KvK status
 * @param kvkId - The ID of the KvK to update
 * @param status - New status
 * @param endDate - End date (optional)
 * @returns Promise<KvKData | null> The updated KvK data or null if not found
 */
export const updateKvKStatus = async (
  kvkId: string, 
  status: 'active' | 'completed' | 'upcoming',
  endDate?: string
): Promise<KvKData | null> => {
  try {
    const response = await axios.patch(`/api/kvks/${kvkId}/status`, null, {
      params: {
        status,
        end_date: endDate
      }
    });
    const kvk = response.data;
    return {
      id: kvk.id.toString(),
      name: kvk.name,
      startDate: kvk.start_date,
      endDate: kvk.end_date,
      status: kvk.status as KvKStatus,
      season: kvk.season,
      createdAt: kvk.created_at,
      updatedAt: kvk.updated_at
    };
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null;
    }
    console.error('Error updating KvK status:', error);
    throw error;
  }
};