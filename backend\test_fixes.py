#!/usr/bin/env python3
"""
Test script to verify the critical fixes implemented in the codebase audit.
This script tests the most important calculation logic and data handling fixes.
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """Test that all critical modules can be imported."""
    print("Testing imports...")
    
    try:
        import models
        print("✅ models.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import models: {e}")
        return False
    
    try:
        import calculations
        print("✅ calculations.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import calculations: {e}")
        return False
    
    try:
        import crud
        print("✅ crud.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import crud: {e}")
        return False
    
    try:
        import services
        print("✅ services.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import services: {e}")
        return False
    
    return True

def test_calculation_logic():
    """Test the fixed calculation logic for negative KP deltas."""
    print("\nTesting calculation logic...")
    
    try:
        from calculations import RoKCalculator, PlayerStats, DeltaStats
        
        # Test case 1: Normal positive KP delta
        current = PlayerStats(
            player_id=1,
            governor_id="12345",
            name="TestPlayer",
            alliance="TestAlliance",
            power=1000000,
            total_kill_points=50000000,
            dead_troops=100000
        )
        
        baseline = PlayerStats(
            player_id=1,
            governor_id="12345",
            name="TestPlayer",
            alliance="TestAlliance",
            power=800000,
            total_kill_points=30000000,
            dead_troops=80000
        )
        
        delta = RoKCalculator.calculate_delta_stats(current, baseline)
        
        assert delta.power_delta == 200000, f"Expected power delta 200000, got {delta.power_delta}"
        assert delta.kill_points_delta == 20000000, f"Expected KP delta 20000000, got {delta.kill_points_delta}"
        assert delta.dead_troops_delta == 20000, f"Expected dead delta 20000, got {delta.dead_troops_delta}"
        
        print("✅ Normal calculation test passed")
        
        # Test case 2: Negative KP delta (should be corrected to 0)
        current_bad = PlayerStats(
            player_id=1,
            governor_id="12345",
            name="TestPlayer",
            alliance="TestAlliance",
            power=1000000,
            total_kill_points=25000000,  # Less than baseline - data error
            dead_troops=100000
        )
        
        delta_bad = RoKCalculator.calculate_delta_stats(current_bad, baseline)
        
        # The fix should set negative KP deltas to 0
        assert delta_bad.kill_points_delta == 0, f"Expected corrected KP delta 0, got {delta_bad.kill_points_delta}"
        
        print("✅ Negative KP delta correction test passed")
        
        # Test case 3: Zeroed player detection
        zeroed_current = PlayerStats(
            player_id=1,
            governor_id="12345",
            name="TestPlayer",
            alliance="TestAlliance",
            power=100000,  # Lost 87.5% of power
            total_kill_points=35000000,
            dead_troops=500000  # High dead troops
        )
        
        delta_zeroed = RoKCalculator.calculate_delta_stats(zeroed_current, baseline)
        
        assert delta_zeroed.is_zeroed == True, f"Expected player to be marked as zeroed"
        
        print("✅ Zeroed player detection test passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Calculation logic test failed: {e}")
        return False

def test_database_models():
    """Test that database models are properly defined."""
    print("\nTesting database models...")
    
    try:
        import models
        
        # Check that DeltaStat uses BigInteger for large values
        delta_stat_columns = models.DeltaStat.__table__.columns
        
        power_delta_col = delta_stat_columns['power_delta']
        kp_delta_col = delta_stat_columns['kill_points_delta']
        dead_delta_col = delta_stat_columns['dead_troops_delta']
        
        # Note: In SQLAlchemy, BigInteger appears as BIGINT in the type string
        assert 'BIGINT' in str(power_delta_col.type).upper(), f"power_delta should be BigInteger, got {power_delta_col.type}"
        assert 'BIGINT' in str(kp_delta_col.type).upper(), f"kill_points_delta should be BigInteger, got {kp_delta_col.type}"
        assert 'BIGINT' in str(dead_delta_col.type).upper(), f"dead_troops_delta should be BigInteger, got {dead_delta_col.type}"
        
        print("✅ Database model types test passed")
        
        # Check foreign key relationships
        assert hasattr(models.Player, 'stats'), "Player should have stats relationship"
        assert hasattr(models.Player, 'deltas'), "Player should have deltas relationship"
        assert hasattr(models.Scan, 'player_stats'), "Scan should have player_stats relationship"
        
        print("✅ Database relationships test passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Database models test failed: {e}")
        return False

def test_crud_functions():
    """Test that CRUD functions are available."""
    print("\nTesting CRUD functions...")
    
    try:
        import crud
        
        # Check that the missing function was added
        assert hasattr(crud, 'get_recent_scans'), "get_recent_scans function should exist"
        
        # Check other critical functions
        critical_functions = [
            'get_player_by_gov_id',
            'get_latest_scan',
            'get_baseline_scan',
            'get_delta_stats_for_scan_pair',
            'create_delta_stat'
        ]
        
        for func_name in critical_functions:
            assert hasattr(crud, func_name), f"Function {func_name} should exist"
        
        print("✅ CRUD functions test passed")
        
        return True
        
    except Exception as e:
        print(f"❌ CRUD functions test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🔍 Running codebase audit fix verification tests...\n")
    
    tests = [
        test_imports,
        test_calculation_logic,
        test_database_models,
        test_crud_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The critical fixes are working correctly.")
        print("\nNext steps:")
        print("1. Run the database migration script")
        print("2. Test with real scan data")
        print("3. Deploy to production environment")
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        print("The application may not work correctly until these issues are resolved.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
