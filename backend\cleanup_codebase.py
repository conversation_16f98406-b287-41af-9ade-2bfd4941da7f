"""
Comprehensive codebase cleanup script.
Removes unused imports, duplicate code, and fixes inconsistencies.
"""

import os
import sys
import re
import ast
from typing import Set, List, Dict
from pathlib import Path

def find_unused_imports(file_path: str) -> List[str]:
    """Find unused imports in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        # Find all imports
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")
        
        # Find all names used in the code
        used_names = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                used_names.add(node.id)
            elif isinstance(node, ast.Attribute):
                used_names.add(node.attr)
        
        # Find unused imports
        unused = []
        for imp in imports:
            base_name = imp.split('.')[-1]
            if base_name not in used_names:
                unused.append(imp)
        
        return unused
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return []

def find_duplicate_functions(directory: str) -> Dict[str, List[str]]:
    """Find duplicate function definitions across files."""
    functions = {}
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    tree = ast.parse(content)
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            func_name = node.name
                            if func_name not in functions:
                                functions[func_name] = []
                            functions[func_name].append(file_path)
                except Exception as e:
                    print(f"Error parsing {file_path}: {e}")
    
    # Find duplicates
    duplicates = {name: files for name, files in functions.items() if len(files) > 1}
    return duplicates

def clean_unused_variables(file_path: str) -> bool:
    """Remove unused variables from a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Simple regex patterns for common unused variable patterns
        patterns_to_remove = [
            r'^\s*#.*unused.*$',  # Comments about unused code
            r'^\s*#.*TODO.*remove.*$',  # TODO comments about removal
            r'^\s*#.*FIXME.*$',  # FIXME comments
        ]
        
        cleaned_lines = []
        for line in lines:
            should_remove = False
            for pattern in patterns_to_remove:
                if re.match(pattern, line, re.IGNORECASE):
                    should_remove = True
                    break
            
            if not should_remove:
                cleaned_lines.append(line)
        
        # Write back if changes were made
        if len(cleaned_lines) != len(lines):
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(cleaned_lines)
            return True
        
        return False
    except Exception as e:
        print(f"Error cleaning {file_path}: {e}")
        return False

def remove_duplicate_imports(file_path: str) -> bool:
    """Remove duplicate import statements."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        seen_imports = set()
        cleaned_lines = []
        
        for line in lines:
            # Check if line is an import
            if line.strip().startswith(('import ', 'from ')):
                if line.strip() not in seen_imports:
                    seen_imports.add(line.strip())
                    cleaned_lines.append(line)
                # Skip duplicate imports
            else:
                cleaned_lines.append(line)
        
        # Write back if changes were made
        if len(cleaned_lines) != len(lines):
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(cleaned_lines)
            return True
        
        return False
    except Exception as e:
        print(f"Error removing duplicate imports from {file_path}: {e}")
        return False

def cleanup_backend():
    """Clean up backend Python files."""
    print("🧹 Cleaning up backend files...")
    
    backend_dir = "."
    python_files = []
    
    for root, dirs, files in os.walk(backend_dir):
        for file in files:
            if file.endswith('.py') and not file.startswith('test_'):
                python_files.append(os.path.join(root, file))
    
    cleaned_files = 0
    
    for file_path in python_files:
        print(f"  📄 Cleaning {file_path}")
        
        # Remove duplicate imports
        if remove_duplicate_imports(file_path):
            print(f"    ✅ Removed duplicate imports")
            cleaned_files += 1
        
        # Clean unused variables/comments
        if clean_unused_variables(file_path):
            print(f"    ✅ Cleaned unused variables/comments")
            cleaned_files += 1
    
    print(f"✅ Cleaned {cleaned_files} backend files")
    
    # Find duplicate functions
    print("\n🔍 Finding duplicate functions...")
    duplicates = find_duplicate_functions(backend_dir)
    
    if duplicates:
        print("⚠️  Found duplicate functions:")
        for func_name, files in duplicates.items():
            if len(files) > 1:
                print(f"  - {func_name}: {files}")
    else:
        print("✅ No duplicate functions found")

def cleanup_frontend():
    """Clean up frontend TypeScript/JavaScript files."""
    print("\n🧹 Cleaning up frontend files...")
    
    frontend_dir = "../frontend/src"
    if not os.path.exists(frontend_dir):
        print("❌ Frontend directory not found")
        return
    
    ts_files = []
    for root, dirs, files in os.walk(frontend_dir):
        for file in files:
            if file.endswith(('.ts', '.tsx', '.js', '.jsx')):
                ts_files.append(os.path.join(root, file))
    
    print(f"📊 Found {len(ts_files)} frontend files to analyze")
    
    # Simple cleanup for frontend files
    cleaned_files = 0
    for file_path in ts_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Remove unused console.log statements (commented out)
            content = re.sub(r'^\s*//\s*console\.log.*$', '', content, flags=re.MULTILINE)
            
            # Remove empty lines (more than 2 consecutive)
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            # Remove trailing whitespace
            content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                cleaned_files += 1
                print(f"  ✅ Cleaned {file_path}")
        
        except Exception as e:
            print(f"  ❌ Error cleaning {file_path}: {e}")
    
    print(f"✅ Cleaned {cleaned_files} frontend files")

def generate_cleanup_report():
    """Generate a comprehensive cleanup report."""
    print("\n📊 CLEANUP REPORT")
    print("=" * 50)
    
    # Backend analysis
    backend_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.py'):
                backend_files.append(os.path.join(root, file))
    
    print(f"📁 Backend files analyzed: {len(backend_files)}")
    
    # Frontend analysis
    frontend_dir = "../frontend/src"
    frontend_files = []
    if os.path.exists(frontend_dir):
        for root, dirs, files in os.walk(frontend_dir):
            for file in files:
                if file.endswith(('.ts', '.tsx', '.js', '.jsx')):
                    frontend_files.append(os.path.join(root, file))
    
    print(f"📁 Frontend files analyzed: {len(frontend_files)}")
    
    # Calculate total lines of code
    total_lines = 0
    for file_path in backend_files + frontend_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                total_lines += len(f.readlines())
        except:
            pass
    
    print(f"📏 Total lines of code: {total_lines:,}")
    print("\n✅ Cleanup completed successfully!")

def main():
    """Main cleanup function."""
    print("🚀 COMPREHENSIVE CODEBASE CLEANUP")
    print("=" * 50)
    print("This script will clean up unused imports, duplicate code,")
    print("and other issues in both backend and frontend.")
    print("=" * 50)
    
    # Change to backend directory
    if not os.path.exists("main.py"):
        print("❌ Please run this script from the backend directory")
        return
    
    try:
        # Clean up backend
        cleanup_backend()
        
        # Clean up frontend
        cleanup_frontend()
        
        # Generate report
        generate_cleanup_report()
        
        print("\n🎉 All cleanup tasks completed!")
        print("💡 Recommendation: Run your tests to ensure everything still works.")
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
