import React, { useState, useCallback } from 'react';
import axios from '../config/axios';
import { useTheme } from '../contexts/ThemeContext';

interface FileUploadProps {
  onUploadSuccess?: (data: any) => void;
  onUploadError?: (error: string) => void;
  kvkId?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess, onUploadError, kvkId }) => {
  const { theme } = useTheme();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [scanName, setScanName] = useState<string>('');
  const [isBaseline, setIsBaseline] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadMessage, setUploadMessage] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
      setUploadMessage(null); // Clear previous messages
    }
  };

  const handleUpload = useCallback(async () => {
    if (!selectedFile) {
      setUploadMessage('Please select a file first.');
      onUploadError?.('Please select a file first.');
      return;
    }
    if (!scanName.trim()) {
      setUploadMessage('Please enter a scan name.');
      onUploadError?.('Please enter a scan name.');
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);
    // Backend expects query parameters for scan_name and is_baseline
    // not form data for these, so we append them to the URL.

    setIsUploading(true);
    setUploadMessage('Uploading...');

    try {
      let url = `/api/scans/upload?scan_name=${encodeURIComponent(scanName)}&is_baseline=${isBaseline}`;

      // Add kvk_id if provided
      if (kvkId) {
        url += `&kvk_id=${kvkId}`;
      }

      const response = await axios.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      setUploadMessage('File uploaded successfully!');
      setSelectedFile(null); // Clear file input
      setScanName(''); // Clear scan name
      setIsBaseline(false); // Reset baseline toggle
      if (onUploadSuccess) {
        onUploadSuccess(response.data);
      }
    } catch (error: any) {
      let errorMessage = 'Error uploading file.';
      if (axios.isAxiosError(error) && error.response) {
        errorMessage = error.response.data.detail || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setUploadMessage(errorMessage);
      if (onUploadError) {
        onUploadError(errorMessage);
      }
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  }, [selectedFile, scanName, isBaseline, onUploadSuccess, onUploadError]);

  return (
    <div className={`p-6 max-w-md mx-auto ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} rounded-lg shadow-lg space-y-6 border ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
      <h2 className={`text-2xl font-bold ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>Upload Scan Data</h2>
      <div>
        <label htmlFor="scanName" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
          Scan Name:
        </label>
        <input
          type="text"
          id="scanName"
          value={scanName}
          onChange={(e) => setScanName(e.target.value)}
          placeholder="e.g., Pre-KVK Week 1"
          className={`block w-full px-4 py-3 ${theme === 'light' ? 'bg-white border-gray-300 text-gray-900' : 'bg-gray-700 border-gray-600 text-white'} border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm`}
          disabled={isUploading}
        />
      </div>
      <div>
        <label htmlFor="fileInput" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
          Select Scan File (CSV or Excel):
        </label>
        <input
          type="file"
          id="fileInput"
          onChange={handleFileChange}
          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          className={`block w-full text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}
            file:mr-4 file:py-2 file:px-4
            file:rounded-md file:border-0
            file:text-sm file:font-medium
            ${theme === 'light'
              ? 'file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
              : 'file:bg-blue-900 file:text-blue-300 hover:file:bg-blue-800'
            }
            disabled:opacity-50`}
          disabled={isUploading}
        />
      </div>
      <div className="flex items-center">
        <input
          id="isBaseline"
          name="isBaseline"
          type="checkbox"
          checked={isBaseline}
          onChange={(e) => setIsBaseline(e.target.checked)}
          className={`h-5 w-5 text-blue-600 ${theme === 'light' ? 'border-gray-300' : 'border-gray-600'} rounded focus:ring-blue-500`}
          disabled={isUploading}
        />
        <label htmlFor="isBaseline" className={`ml-3 block text-sm ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
          Mark as Baseline Scan
        </label>
      </div>
      <button
        onClick={handleUpload}
        disabled={!selectedFile || !scanName.trim() || isUploading}
        className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
      >
        {isUploading ? 'Uploading...' : 'Upload File'}
      </button>
      {uploadMessage && (
        <div className={`mt-2 p-3 rounded-md ${
          uploadMessage.startsWith('Error')
            ? theme === 'light' ? 'bg-red-50 text-red-700' : 'bg-red-900/30 text-red-400'
            : theme === 'light' ? 'bg-green-50 text-green-700' : 'bg-green-900/30 text-green-400'
        }`}>
          <p className="text-sm font-medium">
            {uploadMessage}
          </p>
        </div>
      )}
    </div>
  );
};

export default FileUpload;