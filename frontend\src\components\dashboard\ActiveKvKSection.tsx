import React, { memo } from 'react';
// Link is no longer directly used for the button, ButtonLink will use it.
// import { Link } from 'react-router-dom';
import KvKDashboard from '../KvKDashboard';
import { useTheme } from '../../contexts/ThemeContext'; // Still needed for section title
import ButtonLink from '../common/ButtonLink'; // Import ButtonLink
import { KvKData } from '../../types/dataTypes';
import { KvKScanData } from '../../hooks/useDashboardData'; // Assuming KvKScanData is exported from the hook

interface ActiveKvKSectionProps {
  activeKvK: KvKData | undefined; // Can be undefined if no active KvK
  kvkScans: KvKScanData;
  canManageKvK: boolean;
}

const ActiveKvKSection: React.FC<ActiveKvKSectionProps> = ({ activeKvK, kvkScans, canManageKvK }) => {
  const { theme } = useTheme();

  return (
    <div className="mb-0"> {/* Adjusted margin from original DashboardPage */}
      <div className="flex justify-between items-center mb-4">
        <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Active KvK
        </h2>
        {canManageKvK && (
          <ButtonLink to="/create-kvk" variant="primary" className="text-sm px-3 py-1">
            Create New KvK
          </ButtonLink>
        )}
      </div>

      {activeKvK ? (
        <KvKDashboard
          kvk={activeKvK}
          latestScan={kvkScans.latestScan}
          previousScan={kvkScans.previousScan}
          baselineScan={kvkScans.baselineScan}
        />
      ) : (
        <div className={`p-6 rounded-lg border ${theme === 'light' ? 'bg-gray-50 border-gray-200' : 'bg-gray-800 border-gray-700'}`}>
          <p className={`text-center ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
            No active KvK at the moment.
          </p>
          {canManageKvK && (
            <div className="mt-4 text-center">
              <ButtonLink to="/create-kvk" variant="primary">
                Create New KvK
              </ButtonLink>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default memo(ActiveKvKSection);
