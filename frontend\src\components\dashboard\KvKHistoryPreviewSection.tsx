import React, { memo } from 'react';
// <PERSON> is no longer directly used for the "View All" link if it becomes a ButtonLink,
// but kept for now if only the "Create New KvK" buttons were the target of ButtonLink.
// However, the task is to create a reusable ButtonLink, implying it can be used for various link styles.
// Let's assume "View All KvK History" should also be a ButtonLink for consistency if it acts like a button.
// For now, we'll use ButtonLink for it with a 'ghost' or similar variant.
import { Link } from 'react-router-dom'; // Keep for general links if any, or remove if all become ButtonLink
import KvKDashboard from '../KvKDashboard';
import { useTheme } from '../../contexts/ThemeContext'; // Still needed for section title
import ButtonLink from '../common/ButtonLink'; // Import ButtonLink
import { KvKData } from '../../types/dataTypes';
import { KvKScanData } from '../../hooks/useDashboardData';

interface KvKHistoryPreviewSectionProps {
  completedKvKs: KvKData[]; // Expecting already filtered and sorted list
  getKvKScans: (kvkId: string | undefined) => KvKScanData;
}

const KvKHistoryPreviewSection: React.FC<KvKHistoryPreviewSectionProps> = ({ completedKvKs, getKvKScans }) => {
  const { theme } = useTheme();
  const mostRecentCompletedKvK = completedKvKs.length > 0 ? completedKvKs[0] : null;

  return (
    <div className="mt-0"> {/* Adjusted margin from original DashboardPage */}
      <div className="flex justify-between items-center mb-4">
        <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          KvK History
        </h2>
        <ButtonLink
          to="/kvk-history"
          variant="ghost" // Example variant for a less prominent button-like link
          className="text-sm" // Keep text-sm, specific styling can be added via className
        >
          View All KvK History →
        </ButtonLink>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {mostRecentCompletedKvK ? (
          <KvKDashboard
            key={mostRecentCompletedKvK.id}
            kvk={mostRecentCompletedKvK}
            latestScan={getKvKScans(mostRecentCompletedKvK.id).latestScan}
            previousScan={getKvKScans(mostRecentCompletedKvK.id).previousScan}
            baselineScan={getKvKScans(mostRecentCompletedKvK.id).baselineScan}
          />
        ) : (
          <div className={`p-6 rounded-lg border ${theme === 'light' ? 'bg-gray-50 border-gray-200' : 'bg-gray-800 border-gray-700'}`}>
            <p className={`text-center ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              No completed KvK seasons available yet.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(KvKHistoryPreviewSection); // Wrap with memo
