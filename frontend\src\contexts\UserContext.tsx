import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';

// Define user roles
export enum UserRole {
  ADMIN = 'admin',
  OFFICER = 'officer',
  MEMBER = 'member',
  GUEST = 'guest'
}

// User interface
export interface User {
  id: string;
  username: string;
  role: UserRole;
  alliance?: string;
  governorId?: string;
}

// Context type
interface UserContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
  canUploadScans: () => boolean;
  canViewAdminPages: () => boolean;
  canManageKvK: () => boolean;
}

// Create context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Production authentication - users managed via backend API

// Provider component
export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(() => {
    if (typeof window !== 'undefined') {
      // Use sessionStorage for better security (cleared on tab close)
      const savedUser = sessionStorage.getItem('user');
      const parsedUser = savedUser ? JSON.parse(savedUser) : null;
      console.log('[UserContext] Initial user state:', parsedUser);
      return parsedUser;
    }
    return null;
  });

  const isAuthenticated = user !== null;

  // Save user to sessionStorage when it changes (more secure than localStorage)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (user) {
        sessionStorage.setItem('user', JSON.stringify(user));
      } else {
        sessionStorage.removeItem('user');
        // Also clear any remaining localStorage items for security
        localStorage.removeItem('user');
        localStorage.removeItem('authToken');
      }
    }
  }, [user]);

  // Production login function with enhanced security
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // Sanitize inputs
      const sanitizedUsername = username.trim();
      const sanitizedPassword = password.trim();

      if (!sanitizedUsername || !sanitizedPassword) {
        console.error('Login failed: Empty credentials');
        return false;
      }

      // Make API call to authenticate user with credentials
      const response = await fetch('/api/auth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: sanitizedUsername,
          password: sanitizedPassword,
        }),
        credentials: 'include', // Include cookies for httpOnly token
      });

      if (response.ok) {
        const userData = await response.json();
        const authenticatedUser: User = {
          id: userData.id,
          username: userData.username,
          role: userData.role as UserRole,
          alliance: userData.alliance,
          governorId: userData.governor_id
        };

        setUser(authenticatedUser);
        // Note: Token is now stored in httpOnly cookie by backend
        console.log('[UserContext] Login successful for user:', authenticatedUser.username);
        return true;
      } else {
        console.error('Login failed:', response.status, response.statusText);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      logout(); // Ensure clean state on error
      return false;
    }
  };

  // Enhanced logout function with proper cleanup
  const logout = async () => {
    try {
      // Call backend logout endpoint to clear httpOnly cookies
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout API call failed:', error);
      // Continue with local cleanup even if API call fails
    }

    // Clear local state and storage
    setUser(null);
    sessionStorage.removeItem('user');
    localStorage.removeItem('user');
    localStorage.removeItem('authToken');

    console.log('[UserContext] User logged out and session cleared');
  };

  // Permission checking functions
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    switch (permission) {
      case 'upload_scans':
        return [UserRole.ADMIN].includes(user.role);
      case 'view_admin_pages':
        return [UserRole.ADMIN].includes(user.role);
      case 'manage_kvk':
        return [UserRole.ADMIN].includes(user.role);
      // Everyone can view dashboards and performance data
      case 'view_dashboards':
        return true;
      default:
        return false;
    }
  };

  const canUploadScans = () => hasPermission('upload_scans');
  const canViewAdminPages = () => hasPermission('view_admin_pages');
  const canManageKvK = () => hasPermission('manage_kvk');

  return (
    <UserContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated,
      hasPermission,
      canUploadScans,
      canViewAdminPages,
      canManageKvK
    }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
