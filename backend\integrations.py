"""
External Integrations Module
Provides integrations with external services and APIs for enhanced functionality.
"""

import asyncio
import aiohttp
import smtplib
import json
import csv
import io
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
import logging
from dataclasses import dataclass
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

@dataclass
class NotificationConfig:
    """Notification configuration."""
    email_enabled: bool = False
    webhook_enabled: bool = False
    discord_enabled: bool = False
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    webhook_url: Optional[str] = None
    discord_webhook_url: Optional[str] = None

class EmailService:
    """Email notification service."""
    
    def __init__(self, config: NotificationConfig):
        self.config = config
    
    async def send_email(self, to_emails: List[str], subject: str, 
                        body: str, html_body: str = None, 
                        attachments: List[Dict[str, Any]] = None) -> bool:
        """Send email notification."""
        if not self.config.email_enabled or not self.config.smtp_host:
            logger.warning("Email service not configured")
            return False
        
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.config.smtp_username
            msg['To'] = ', '.join(to_emails)
            
            # Add text body
            text_part = MIMEText(body, 'plain')
            msg.attach(text_part)
            
            # Add HTML body if provided
            if html_body:
                html_part = MIMEText(html_body, 'html')
                msg.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['data'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # Send email
            with smtplib.SMTP(self.config.smtp_host, self.config.smtp_port) as server:
                server.starttls()
                server.login(self.config.smtp_username, self.config.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {to_emails}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False
    
    async def send_performance_report(self, to_emails: List[str], 
                                    report_data: Dict[str, Any]) -> bool:
        """Send performance report via email."""
        subject = f"Kingdom Performance Report - {datetime.now().strftime('%Y-%m-%d')}"
        
        # Generate text body
        body = f"""
Kingdom Performance Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Kingdom Overview:
- Total Players: {report_data.get('total_players', 'N/A'):,}
- Total Power: {report_data.get('total_power', 0):,}
- Total Kill Points: {report_data.get('total_kp', 0):,}

Top Performers:
"""
        
        for i, performer in enumerate(report_data.get('top_performers', [])[:5], 1):
            body += f"{i}. {performer.get('name', 'Unknown')} - {performer.get('kill_points_delta', 0):,} KP\n"
        
        # Generate HTML body
        html_body = f"""
        <html>
        <body>
            <h2>Kingdom Performance Report</h2>
            <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <h3>Kingdom Overview</h3>
            <ul>
                <li><strong>Total Players:</strong> {report_data.get('total_players', 'N/A'):,}</li>
                <li><strong>Total Power:</strong> {report_data.get('total_power', 0):,}</li>
                <li><strong>Total Kill Points:</strong> {report_data.get('total_kp', 0):,}</li>
            </ul>
            
            <h3>Top Performers</h3>
            <ol>
        """
        
        for performer in report_data.get('top_performers', [])[:5]:
            html_body += f"<li>{performer.get('name', 'Unknown')} - {performer.get('kill_points_delta', 0):,} KP</li>"
        
        html_body += """
            </ol>
        </body>
        </html>
        """
        
        return await self.send_email(to_emails, subject, body, html_body)

class WebhookService:
    """Generic webhook notification service."""
    
    def __init__(self, config: NotificationConfig):
        self.config = config
    
    async def send_webhook(self, url: str, payload: Dict[str, Any], 
                          headers: Dict[str, str] = None) -> bool:
        """Send webhook notification."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=payload,
                    headers=headers or {'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 200:
                        logger.info(f"Webhook sent successfully to {url}")
                        return True
                    else:
                        logger.error(f"Webhook failed with status {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Failed to send webhook: {str(e)}")
            return False

class DiscordIntegration:
    """Discord webhook integration."""
    
    def __init__(self, config: NotificationConfig):
        self.config = config
        self.webhook_service = WebhookService(config)
    
    async def send_discord_message(self, message: str, embeds: List[Dict] = None) -> bool:
        """Send message to Discord channel."""
        if not self.config.discord_enabled or not self.config.discord_webhook_url:
            logger.warning("Discord integration not configured")
            return False
        
        payload = {
            'content': message,
            'username': 'Kingdom Tracker',
            'avatar_url': 'https://example.com/bot-avatar.png'  # Optional bot avatar
        }
        
        if embeds:
            payload['embeds'] = embeds
        
        return await self.webhook_service.send_webhook(
            self.config.discord_webhook_url,
            payload
        )
    
    async def send_performance_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send performance alert to Discord."""
        embed = {
            'title': '⚠️ Performance Alert',
            'description': alert_data.get('message', 'Performance threshold exceeded'),
            'color': 0xff6b6b,  # Red color
            'timestamp': datetime.utcnow().isoformat(),
            'fields': [
                {
                    'name': 'Player',
                    'value': alert_data.get('player_name', 'Unknown'),
                    'inline': True
                },
                {
                    'name': 'Alert Type',
                    'value': alert_data.get('alert_type', 'Unknown'),
                    'inline': True
                },
                {
                    'name': 'Current Value',
                    'value': str(alert_data.get('current_value', 'N/A')),
                    'inline': True
                }
            ]
        }
        
        return await self.send_discord_message('', [embed])
    
    async def send_kvk_update(self, kvk_data: Dict[str, Any]) -> bool:
        """Send KvK update to Discord."""
        embed = {
            'title': '⚔️ KvK Update',
            'description': f"KvK: {kvk_data.get('name', 'Unknown')}",
            'color': 0x4CAF50,  # Green color
            'timestamp': datetime.utcnow().isoformat(),
            'fields': [
                {
                    'name': 'Status',
                    'value': kvk_data.get('status', 'Unknown'),
                    'inline': True
                },
                {
                    'name': 'Total KP',
                    'value': f"{kvk_data.get('total_kp', 0):,}",
                    'inline': True
                },
                {
                    'name': 'Participants',
                    'value': str(kvk_data.get('participant_count', 0)),
                    'inline': True
                }
            ]
        }
        
        return await self.send_discord_message('', [embed])

class DataExportService:
    """Data export service for various formats."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def export_to_csv(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """Export data to CSV format."""
        if not data:
            return ""
        
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
        
        return output.getvalue()
    
    def export_performance_data(self, format: str = 'csv') -> str:
        """Export performance data in specified format."""
        # Get performance data from database
        from services import get_general_performance_summary
        
        try:
            performance_data = get_general_performance_summary(self.db, limit=1000)
            
            if format.lower() == 'csv':
                return self.export_to_csv(performance_data.get('performance_data', []))
            elif format.lower() == 'json':
                return json.dumps(performance_data, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            logger.error(f"Failed to export performance data: {str(e)}")
            return ""
    
    def export_player_list(self, format: str = 'csv') -> str:
        """Export player list in specified format."""
        try:
            from crud import get_players
            
            players = get_players(self.db, limit=10000)
            player_data = [
                {
                    'governor_id': player.governor_id,
                    'name': player.name,
                    'alliance': player.alliance,
                    'created_at': player.created_at.isoformat() if player.created_at else None
                }
                for player in players
            ]
            
            if format.lower() == 'csv':
                return self.export_to_csv(player_data)
            elif format.lower() == 'json':
                return json.dumps(player_data, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            logger.error(f"Failed to export player list: {str(e)}")
            return ""

class BackupService:
    """Database backup and restore service."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_backup(self, backup_path: str = None) -> str:
        """Create database backup."""
        try:
            backup_filename = backup_path or f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
            # For SQLite, copy the database file
            import shutil
            from database import DATABASE_URL
            
            if 'sqlite' in DATABASE_URL:
                db_path = DATABASE_URL.replace('sqlite:///', '')
                shutil.copy2(db_path, backup_filename)
                logger.info(f"Database backup created: {backup_filename}")
                return backup_filename
            else:
                # For PostgreSQL, use pg_dump (would need to be implemented)
                logger.warning("PostgreSQL backup not implemented")
                return ""
                
        except Exception as e:
            logger.error(f"Failed to create backup: {str(e)}")
            return ""
    
    async def schedule_backup(self, interval_hours: int = 24):
        """Schedule automatic backups."""
        while True:
            try:
                backup_file = await self.create_backup()
                if backup_file:
                    logger.info(f"Scheduled backup completed: {backup_file}")
                else:
                    logger.error("Scheduled backup failed")
                
                # Wait for next backup
                await asyncio.sleep(interval_hours * 3600)
                
            except Exception as e:
                logger.error(f"Error in backup scheduler: {str(e)}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying

class IntegrationManager:
    """Main integration manager."""
    
    def __init__(self, config: NotificationConfig, db: Session):
        self.config = config
        self.db = db
        self.email_service = EmailService(config)
        self.discord_integration = DiscordIntegration(config)
        self.export_service = DataExportService(db)
        self.backup_service = BackupService(db)
    
    async def send_alert(self, alert_type: str, data: Dict[str, Any]) -> bool:
        """Send alert through all configured channels."""
        success = True
        
        # Send Discord alert
        if self.config.discord_enabled:
            if alert_type == 'performance':
                discord_success = await self.discord_integration.send_performance_alert(data)
                success = success and discord_success
            elif alert_type == 'kvk_update':
                discord_success = await self.discord_integration.send_kvk_update(data)
                success = success and discord_success
        
        # Send email alert
        if self.config.email_enabled and data.get('email_recipients'):
            email_success = await self.email_service.send_performance_report(
                data['email_recipients'], 
                data
            )
            success = success and email_success
        
        return success
    
    async def export_and_send_report(self, report_type: str, 
                                   recipients: List[str], 
                                   format: str = 'csv') -> bool:
        """Export data and send as email attachment."""
        try:
            if report_type == 'performance':
                data = self.export_service.export_performance_data(format)
                filename = f"performance_report_{datetime.now().strftime('%Y%m%d')}.{format}"
            elif report_type == 'players':
                data = self.export_service.export_player_list(format)
                filename = f"player_list_{datetime.now().strftime('%Y%m%d')}.{format}"
            else:
                logger.error(f"Unknown report type: {report_type}")
                return False
            
            if not data:
                logger.error("No data to export")
                return False
            
            # Send email with attachment
            attachments = [{
                'filename': filename,
                'data': data.encode('utf-8')
            }]
            
            return await self.email_service.send_email(
                recipients,
                f"Kingdom Data Export - {report_type.title()}",
                f"Please find attached the {report_type} data export.",
                attachments=attachments
            )
            
        except Exception as e:
            logger.error(f"Failed to export and send report: {str(e)}")
            return False

# Export main components
__all__ = [
    'IntegrationManager',
    'EmailService',
    'DiscordIntegration',
    'DataExportService',
    'BackupService',
    'NotificationConfig'
]
