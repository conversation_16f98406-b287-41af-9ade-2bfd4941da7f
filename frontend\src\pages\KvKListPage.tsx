import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { KvKData } from '../types/dataTypes';
import { getKvKList } from '../services/kvkService';

const KvKListPage: React.FC = () => {
  const { theme } = useTheme();

  // Fetch KvK list using React Query
  const { data: kvks = [], isLoading } = useQuery({
    queryKey: ['kvkList'],
    queryFn: getKvKList
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className={`text-2xl font-bold ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
          KvK Seasons
        </h1>
        <Link
          to="/kvk/create"
          className={`px-4 py-2 rounded-md ${theme === 'light' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white inline-block`}
        >
          New KvK Season
        </Link>
      </div>

      <div className={`overflow-hidden shadow-md rounded-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className={`${theme === 'light' ? 'bg-gray-50' : 'bg-gray-700'}`}>
            <tr>
              <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'} uppercase tracking-wider`}>
                Season
              </th>
              <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'} uppercase tracking-wider`}>
                Name
              </th>
              <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'} uppercase tracking-wider`}>
                Start Date
              </th>
              <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'} uppercase tracking-wider`}>
                Status
              </th>
              <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'} uppercase tracking-wider`}>
                Scans
              </th>
              <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'} uppercase tracking-wider`}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
            {kvks.map((kvk) => (
              <tr key={kvk.id} className={`${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'}`}>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {kvk.season}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  <Link to={`/kvk/${kvk.id}`} className={`${theme === 'light' ? 'text-blue-600 hover:text-blue-900' : 'text-blue-400 hover:text-blue-300'}`}>
                    {kvk.name}
                  </Link>
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                  {new Date(kvk.startDate).toLocaleDateString()}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm`}>
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${kvk.status === 'active'
                      ? `${theme === 'light' ? 'bg-green-100 text-green-800' : 'bg-green-900 text-green-200'}`
                      : kvk.status === 'completed'
                        ? `${theme === 'light' ? 'bg-gray-100 text-gray-800' : 'bg-gray-900 text-gray-200'}`
                        : `${theme === 'light' ? 'bg-yellow-100 text-yellow-800' : 'bg-yellow-900 text-yellow-200'}`
                    }`}>
                    {kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}
                  </span>
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                  {kvk.scans.length}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-right ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                  <Link to={`/kvk/${kvk.id}`} className={`${theme === 'light' ? 'text-blue-600 hover:text-blue-900' : 'text-blue-400 hover:text-blue-300'} mr-4`}>
                    View
                  </Link>
                  <button
                    className={`${theme === 'light' ? 'text-blue-600 hover:text-blue-900' : 'text-blue-400 hover:text-blue-300'}`}
                    onClick={() => {
                      // Open a modal to edit the KvK
                      alert(`Edit KvK ${kvk.name} functionality would go here`);
                    }}
                  >
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default KvKListPage;
