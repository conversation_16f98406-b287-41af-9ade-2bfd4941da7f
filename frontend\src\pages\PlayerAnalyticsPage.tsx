import React, { useState, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { getGeneralPerformanceSummary } from '../api/api';
import { formatLargeNumber } from '../utils/formatters';
import { useDocumentTitle, PAGE_TITLES } from '../hooks/useDocumentTitle';
import {
  Fa<PERSON><PERSON>,
  FaSearch,
  FaFilter,
  FaSort,
  FaChartLine,
  FaShieldAlt,
  FaCrosshairs,
  FaSkullCrossbones,
  FaTrophy,
  FaExclamationTriangle,
  FaArrowUp,
  FaArrowDown,
  FaMinus
} from 'react-icons/fa';

interface PlayerAnalyticsData {
  player_id: string;
  player_name: string;
  governor_id: string;
  alliance: string;
  current_power: number;
  power_delta: number;
  kp_delta: number;
  deads_delta: number;
  t45_kills_delta: number;
  efficiency_ratio: number;
  is_new_player?: boolean;
  is_zeroed?: boolean;
}

const PlayerAnalyticsPage: React.FC = () => {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<keyof PlayerAnalyticsData>('kp_delta');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterBy, setFilterBy] = useState<'all' | 'gainers' | 'losers' | 'inactive' | 'new' | 'zeroed'>('all');

  // Set document title
  useDocumentTitle('Player Analytics - Kingdom 2358');

  // Fetch player performance data
  const { data: performanceData, isLoading, error } = useQuery({
    queryKey: ['playerAnalytics'],
    queryFn: () => getGeneralPerformanceSummary(500), // Get more players for analytics
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000,
  });

  // Process and filter player data
  const processedPlayers = useMemo(() => {
    if (!performanceData?.performance_data) return [];

    let players = performanceData.performance_data.map((player: any) => ({
      player_id: player.player_id?.toString() || '',
      player_name: player.player_name || 'Unknown',
      governor_id: player.governor_id || '',
      alliance: player.alliance || 'No Alliance',
      current_power: player.current_power || 0,
      power_delta: player.power_delta || 0,
      kp_delta: player.kp_delta || 0,
      deads_delta: player.dead_delta || 0,
      t45_kills_delta: player.t45_delta || 0,
      efficiency_ratio: (player.dead_delta || 0) > 0 ? ((player.kp_delta || 0) / (player.dead_delta || 0)) : 0,
      is_new_player: player.is_new_player || false,
      is_zeroed: player.is_zeroed || false
    }));

    // Apply search filter
    if (searchTerm) {
      players = players.filter(player =>
        player.player_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        player.alliance.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'gainers':
        players = players.filter(player => player.kp_delta > 0);
        break;
      case 'losers':
        players = players.filter(player => player.power_delta < 0);
        break;
      case 'inactive':
        players = players.filter(player => player.kp_delta === 0 && !player.is_new_player);
        break;
      case 'new':
        players = players.filter(player => player.is_new_player);
        break;
      case 'zeroed':
        players = players.filter(player => player.is_zeroed);
        break;
    }

    // Apply sorting
    players.sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      const multiplier = sortOrder === 'desc' ? -1 : 1;

      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return aVal.localeCompare(bVal) * multiplier;
      }
      return ((aVal as number) - (bVal as number)) * multiplier;
    });

    return players;
  }, [performanceData, searchTerm, sortBy, sortOrder, filterBy]);

  // Calculate analytics summary
  const analyticsSummary = useMemo(() => {
    if (!processedPlayers.length) return null;

    const totalPlayers = processedPlayers.length;
    const activePlayers = processedPlayers.filter(p => p.kp_delta > 0).length;
    const powerGainers = processedPlayers.filter(p => p.power_delta > 0).length;
    const powerLosers = processedPlayers.filter(p => p.power_delta < 0).length;

    // Calculate averages only for active players to get meaningful metrics
    const avgKpGain = activePlayers > 0
      ? processedPlayers.filter(p => p.kp_delta > 0).reduce((sum, p) => sum + p.kp_delta, 0) / activePlayers
      : 0;

    // Calculate average power change for all players (including losses)
    const avgPowerChange = processedPlayers.reduce((sum, p) => sum + p.power_delta, 0) / totalPlayers;

    return {
      totalPlayers,
      activePlayers,
      powerGainers,
      powerLosers,
      avgKpGain,
      avgPowerChange,
      participationRate: (activePlayers / totalPlayers) * 100
    };
  }, [processedPlayers]);

  const handleSort = (field: keyof PlayerAnalyticsData) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const getSortIcon = (field: keyof PlayerAnalyticsData) => {
    if (sortBy !== field) return <FaSort className="opacity-50" />;
    return sortOrder === 'desc' ? <FaArrowDown /> : <FaArrowUp />;
  };

  const getPowerChangeIcon = (delta: number) => {
    if (delta > 0) return <FaArrowUp className="text-green-500" />;
    if (delta < 0) return <FaArrowDown className="text-red-500" />;
    return <FaMinus className="text-gray-400" />;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading player analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <FaExclamationTriangle className="text-4xl mx-auto mb-4" />
          <p>Error loading player analytics</p>
          <p className="text-sm mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaUser className="text-2xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Player Analytics
                </h1>
                <p className={`text-lg mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Detailed individual player performance analysis and insights
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Summary */}
        {analyticsSummary && (
          <section className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Total Players
                    </p>
                    <p className={`text-3xl font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {analyticsSummary.totalPlayers}
                    </p>
                  </div>
                  <FaUser className={`text-2xl ${
                    theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                  }`} />
                </div>
              </div>

              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Active Players
                    </p>
                    <p className={`text-3xl font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {analyticsSummary.activePlayers}
                    </p>
                    <p className="text-sm text-green-600">
                      {analyticsSummary.participationRate.toFixed(1)}% participation
                    </p>
                  </div>
                  <FaTrophy className="text-2xl text-green-600" />
                </div>
              </div>

              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Avg KP Gain
                    </p>
                    <p className={`text-3xl font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {formatLargeNumber(analyticsSummary.avgKpGain)}
                    </p>
                  </div>
                  <FaCrosshairs className="text-2xl text-red-600" />
                </div>
              </div>

              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Avg Power Change
                    </p>
                    <p className={`text-3xl font-bold ${
                      analyticsSummary.avgPowerChange >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {analyticsSummary.avgPowerChange >= 0 ? '+' : ''}{formatLargeNumber(analyticsSummary.avgPowerChange)}
                    </p>
                  </div>
                  <FaShieldAlt className={`text-2xl ${
                    analyticsSummary.avgPowerChange >= 0 ? 'text-green-600' : 'text-red-600'
                  }`} />
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Filters and Search */}
        <section className="mb-8">
          <div className={`p-6 rounded-2xl shadow-xl ${
            theme === 'light' ? 'bg-white' : 'bg-gray-800'
          }`}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search players or alliances..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                    theme === 'light'
                      ? 'bg-gray-50 border-gray-300 text-gray-900'
                      : 'bg-gray-700 border-gray-600 text-white'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
              </div>

              {/* Filter */}
              <div className="relative">
                <FaFilter className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                  className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                    theme === 'light'
                      ? 'bg-gray-50 border-gray-300 text-gray-900'
                      : 'bg-gray-700 border-gray-600 text-white'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                >
                  <option value="all">All Players</option>
                  <option value="gainers">KP Gainers</option>
                  <option value="losers">Power Losers</option>
                  <option value="inactive">Inactive Players</option>
                  <option value="new">New Players</option>
                  <option value="zeroed">Zeroed Players</option>
                </select>
              </div>

              {/* Results Count */}
              <div className="flex items-center justify-center">
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  Showing {processedPlayers.length} players
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Player Table */}
        <section>
          <div className={`rounded-2xl shadow-xl overflow-hidden ${
            theme === 'light' ? 'bg-white' : 'bg-gray-800'
          }`}>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${
                  theme === 'light' ? 'bg-gray-50' : 'bg-gray-700'
                }`}>
                  <tr>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('player_name')}>
                      <div className="flex items-center space-x-1">
                        <span>Player</span>
                        {getSortIcon('player_name')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('alliance')}>
                      <div className="flex items-center space-x-1">
                        <span>Alliance</span>
                        {getSortIcon('alliance')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('current_power')}>
                      <div className="flex items-center space-x-1">
                        <span>Current Power</span>
                        {getSortIcon('current_power')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('power_delta')}>
                      <div className="flex items-center space-x-1">
                        <span>Power Change</span>
                        {getSortIcon('power_delta')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('kp_delta')}>
                      <div className="flex items-center space-x-1">
                        <span>KP Gain</span>
                        {getSortIcon('kp_delta')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('deads_delta')}>
                      <div className="flex items-center space-x-1">
                        <span>Dead Troops</span>
                        {getSortIcon('deads_delta')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('efficiency_ratio')}>
                      <div className="flex items-center space-x-1">
                        <span>Efficiency</span>
                        {getSortIcon('efficiency_ratio')}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className={`divide-y ${
                  theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'
                }`}>
                  {processedPlayers.map((player, index) => (
                    <tr key={player.player_id} className={`hover:bg-opacity-50 ${
                      theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'
                    }`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white mr-3 ${
                            index < 3 ? 'bg-yellow-500' : 'bg-gray-500'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <div className={`text-sm font-medium flex items-center space-x-2 ${
                              theme === 'light' ? 'text-gray-900' : 'text-white'
                            }`}>
                              <span>{player.player_name}</span>
                              {player.is_new_player && (
                                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full font-semibold">
                                  NEW
                                </span>
                              )}
                              {player.is_zeroed && (
                                <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-semibold">
                                  ZEROED
                                </span>
                              )}
                            </div>
                            <div className={`text-xs ${
                              theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              ID: {player.governor_id || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          player.alliance === 'No Alliance'
                            ? 'bg-gray-100 text-gray-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {player.alliance}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          theme === 'light' ? 'text-gray-900' : 'text-white'
                        }`}>
                          {formatLargeNumber(player.current_power)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getPowerChangeIcon(player.power_delta)}
                          <span className={`ml-2 text-sm font-medium ${
                            player.power_delta > 0 ? 'text-green-600' :
                            player.power_delta < 0 ? 'text-red-600' : 'text-gray-500'
                          }`}>
                            {player.power_delta !== 0 && (player.power_delta > 0 ? '+' : '')}{formatLargeNumber(player.power_delta)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          player.kp_delta > 0 ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {player.kp_delta > 0 ? '+' : ''}{formatLargeNumber(player.kp_delta)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          theme === 'light' ? 'text-gray-900' : 'text-white'
                        }`}>
                          {formatLargeNumber(player.deads_delta)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          player.efficiency_ratio > 10 ? 'text-green-600' :
                          player.efficiency_ratio > 5 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {player.efficiency_ratio > 0 ? player.efficiency_ratio.toFixed(1) : '-'}
                        </div>
                        <div className={`text-xs ${
                          theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                        }`}>
                          KP/Dead
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {processedPlayers.length === 0 && (
              <div className="text-center py-12">
                <FaUser className={`text-4xl mx-auto mb-4 ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-600'
                }`} />
                <p className={`text-lg font-medium ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  No players found
                </p>
                <p className={`text-sm ${
                  theme === 'light' ? 'text-gray-500' : 'text-gray-500'
                }`}>
                  Try adjusting your search or filter criteria
                </p>
              </div>
            )}
          </div>
        </section>
      </div>
    </div>
  );
};

export default PlayerAnalyticsPage;