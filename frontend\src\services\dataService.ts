/**
 * Data Service Layer
 * Manages API calls and state updates through the centralized store
 */

import { useDataStore } from '../store/dataStore';
import * as api from '../api/api';

class DataService {
  private store = useDataStore.getState();

  /**
   * Dashboard Data Management
   */
  async fetchDashboardData(force = false): Promise<any> {
    const { dashboardData, isCacheValid, setDashboardLoading, setDashboardData, setDashboardError } = this.store;

    // Return cached data if valid and not forced
    if (!force && dashboardData && isCacheValid('dashboard')) {
      console.log('[DataService] Using cached dashboard data');
      return dashboardData;
    }

    try {
      setDashboardLoading(true);
      console.log('[DataService] Fetching fresh dashboard data');
      
      const data = await api.getDashboardData();
      setDashboardData(data);
      
      return data;
    } catch (error) {
      console.error('[DataService] Dashboard fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch dashboard data';
      setDashboardError(errorMessage);
      throw error;
    } finally {
      setDashboardLoading(false);
    }
  }

  /**
   * Performance Data Management
   */
  async fetchPerformanceData(force = false): Promise<any> {
    const { performanceData, isCacheValid, setPerformanceLoading, setPerformanceData, setPerformanceError } = this.store;

    if (!force && performanceData && isCacheValid('performance')) {
      console.log('[DataService] Using cached performance data');
      return performanceData;
    }

    try {
      setPerformanceLoading(true);
      console.log('[DataService] Fetching fresh performance data');
      
      const data = await api.getGeneralPerformanceSummary(50);
      setPerformanceData(data);
      
      return data;
    } catch (error) {
      console.error('[DataService] Performance fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch performance data';
      setPerformanceError(errorMessage);
      throw error;
    } finally {
      setPerformanceLoading(false);
    }
  }

  async fetchPerformance7DaysData(): Promise<any> {
    const { setPerformance7DaysData, setPerformanceError } = this.store;

    try {
      console.log('[DataService] Fetching 7-day performance data');
      
      const data = await api.getPerformance7DaysSummary();
      setPerformance7DaysData(data);
      
      return data;
    } catch (error) {
      console.error('[DataService] 7-day performance fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch 7-day performance data';
      setPerformanceError(errorMessage);
      throw error;
    }
  }

  /**
   * KvK Data Management
   */
  async fetchKvKList(force = false): Promise<any[]> {
    const { kvkList, isCacheValid, setKvKLoading, setKvKList, setKvKError, setActiveKvK } = this.store;

    if (!force && kvkList.length > 0 && isCacheValid('kvk')) {
      console.log('[DataService] Using cached KvK list');
      return kvkList;
    }

    try {
      setKvKLoading(true);
      console.log('[DataService] Fetching fresh KvK list');
      
      const data = await api.getKvKList();
      setKvKList(data);
      
      // Set active KvK if not already set
      const currentActive = this.store.activeKvK;
      if (!currentActive && data.length > 0) {
        const active = data.find(kvk => kvk.status === 'active') || 
                      data.find(kvk => kvk.status === 'upcoming') || 
                      data[0];
        setActiveKvK(active);
      }
      
      return data;
    } catch (error) {
      console.error('[DataService] KvK list fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch KvK list';
      setKvKError(errorMessage);
      throw error;
    } finally {
      setKvKLoading(false);
    }
  }

  async fetchKvKSummary(kvkId: string): Promise<any> {
    try {
      console.log(`[DataService] Fetching KvK summary for ${kvkId}`);
      return await api.getKvKSummary(kvkId);
    } catch (error) {
      console.error('[DataService] KvK summary fetch error:', error);
      throw error;
    }
  }

  async fetchKvKPerformance(kvkId: string, limit = 50): Promise<any> {
    try {
      console.log(`[DataService] Fetching KvK performance for ${kvkId}`);
      return await api.getKvKPerformanceSummary(kvkId, limit);
    } catch (error) {
      console.error('[DataService] KvK performance fetch error:', error);
      throw error;
    }
  }

  /**
   * Scan Data Management
   */
  async fetchScanList(force = false): Promise<any[]> {
    const { scanList, isCacheValid, setScanLoading, setScanList, setScanError } = this.store;

    if (!force && scanList.length > 0 && isCacheValid('scans')) {
      console.log('[DataService] Using cached scan list');
      return scanList;
    }

    try {
      setScanLoading(true);
      console.log('[DataService] Fetching fresh scan list');
      
      const data = await api.getScanList();
      setScanList(data);
      
      return data;
    } catch (error) {
      console.error('[DataService] Scan list fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch scan list';
      setScanError(errorMessage);
      throw error;
    } finally {
      setScanLoading(false);
    }
  }

  /**
   * Cache Management
   */
  invalidateCache(type: 'dashboard' | 'performance' | 'kvk' | 'scans' | 'players' | 'all'): void {
    console.log(`[DataService] Invalidating ${type} cache`);
    this.store.refreshData(type);
  }

  clearAllData(): void {
    console.log('[DataService] Clearing all cached data');
    this.store.clearAllData();
  }

  /**
   * Data Consistency Checks
   */
  validateDataConsistency(): {
    isConsistent: boolean;
    issues: string[];
  } {
    const issues: string[] = [];
    const { dashboardData, performanceData, kvkList } = this.store;

    // Check if dashboard and performance data have consistent player counts
    if (dashboardData && performanceData) {
      const dashboardPlayerCount = dashboardData.player_count || 0;
      const performancePlayerCount = performanceData.summary?.total_players || 0;
      
      if (Math.abs(dashboardPlayerCount - performancePlayerCount) > 5) {
        issues.push(`Player count mismatch: Dashboard (${dashboardPlayerCount}) vs Performance (${performancePlayerCount})`);
      }
    }

    // Check if KvK data is consistent
    if (kvkList.length > 0) {
      const activeKvKs = kvkList.filter(kvk => kvk.status === 'active');
      if (activeKvKs.length > 1) {
        issues.push(`Multiple active KvKs found: ${activeKvKs.length}`);
      }
    }

    return {
      isConsistent: issues.length === 0,
      issues
    };
  }

  /**
   * Batch Data Refresh
   */
  async refreshAllData(): Promise<void> {
    console.log('[DataService] Refreshing all data');
    
    try {
      await Promise.allSettled([
        this.fetchDashboardData(true),
        this.fetchPerformanceData(true),
        this.fetchPerformance7DaysData(),
        this.fetchKvKList(true),
        this.fetchScanList(true)
      ]);
      
      // Validate consistency after refresh
      const validation = this.validateDataConsistency();
      if (!validation.isConsistent) {
        console.warn('[DataService] Data consistency issues detected:', validation.issues);
      }
      
    } catch (error) {
      console.error('[DataService] Error during batch refresh:', error);
      throw error;
    }
  }

  /**
   * Get current store state
   */
  getState() {
    return this.store;
  }
}

// Export singleton instance
export const dataService = new DataService();

// Export hooks for React components
export const useDataService = () => {
  return {
    fetchDashboardData: dataService.fetchDashboardData.bind(dataService),
    fetchPerformanceData: dataService.fetchPerformanceData.bind(dataService),
    fetchPerformance7DaysData: dataService.fetchPerformance7DaysData.bind(dataService),
    fetchKvKList: dataService.fetchKvKList.bind(dataService),
    fetchKvKSummary: dataService.fetchKvKSummary.bind(dataService),
    fetchKvKPerformance: dataService.fetchKvKPerformance.bind(dataService),
    fetchScanList: dataService.fetchScanList.bind(dataService),
    invalidateCache: dataService.invalidateCache.bind(dataService),
    clearAllData: dataService.clearAllData.bind(dataService),
    validateDataConsistency: dataService.validateDataConsistency.bind(dataService),
    refreshAllData: dataService.refreshAllData.bind(dataService),
  };
};
