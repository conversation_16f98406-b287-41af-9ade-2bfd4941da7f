import React, { useState, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';
import KvKPlayerTable from './KvKPlayerTable';
import EnhancedMetricsBar from './EnhancedMetricsBar';

interface KvKPerformanceDashboardProps {
  players: PlayerScanData[];
  summaryStats?: {
    total_players: number;
    total_power_gain: number;
    total_power_loss: number;
    net_power_change: number;
    total_kp_gain: number;
    total_dead_troops: number;
    top_performers: number;
    needs_improvement: number;
    power_loss_players: number;
  };
  isBaselineOnly?: boolean;
}

const KvKPerformanceDashboard: React.FC<KvKPerformanceDashboardProps> = ({
  players,
  summaryStats,
  isBaselineOnly = false
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'killpoints' | 'power' | 'deads' | 'powerLoss' | 'underperforming'>('killpoints');

  // Process players based on active tab
  const processedPlayers = useMemo(() => {
    let sortedPlayers = [...players];

    switch (activeTab) {
      case 'killpoints':
        return sortedPlayers.sort((a, b) => {
          if (isBaselineOnly) {
            const kpDiff = (b.killPoints || 0) - (a.killPoints || 0);
            if (kpDiff !== 0) return kpDiff;
            const powerDiff = (b.power || 0) - (a.power || 0);
            if (powerDiff !== 0) return powerDiff;
            return (a.name || '').localeCompare(b.name || '');
          } else {
            const kpGainDiff = (b.killPointsGain || 0) - (a.killPointsGain || 0);
            if (kpGainDiff !== 0) return kpGainDiff;
            const powerGainDiff = (b.powerGain || 0) - (a.powerGain || 0);
            if (powerGainDiff !== 0) return powerGainDiff;
            return (a.name || '').localeCompare(b.name || '');
          }
        });

      case 'power':
        return sortedPlayers.sort((a, b) => {
          if (isBaselineOnly) {
            const powerDiff = (b.power || 0) - (a.power || 0);
            if (powerDiff !== 0) return powerDiff;
            const kpDiff = (b.killPoints || 0) - (a.killPoints || 0);
            if (kpDiff !== 0) return kpDiff;
            return (a.name || '').localeCompare(b.name || '');
          } else {
            const powerGainDiff = (b.powerGain || 0) - (a.powerGain || 0);
            if (powerGainDiff !== 0) return powerGainDiff;
            const kpGainDiff = (b.killPointsGain || 0) - (a.killPointsGain || 0);
            if (kpGainDiff !== 0) return kpGainDiff;
            return (a.name || '').localeCompare(b.name || '');
          }
        });

      case 'powerLoss':
        // Show power losses (negative values, sorted by highest loss amount)
        // Power loss is represented by negative powerGain or power if it's already delta
        return sortedPlayers
          .filter(p => (p.powerGain !== undefined ? p.powerGain : p.power || 0) < 0)
          .sort((a, b) => {
            const lossA = a.powerGain !== undefined ? a.powerGain : a.power || 0;
            const lossB = b.powerGain !== undefined ? b.powerGain : b.power || 0;
            const lossDiff = lossA - lossB; // Sorts most negative first (e.g., -100 before -10)
            if (lossDiff !== 0) return lossDiff;
            return (a.name || '').localeCompare(b.name || '');
          });

      case 'deads':
        return sortedPlayers.sort((a, b) => {
          if (isBaselineOnly) {
            const deadsDiff = (b.deads || 0) - (a.deads || 0);
            if (deadsDiff !== 0) return deadsDiff;
            const powerDiff = (b.power || 0) - (a.power || 0);
            if (powerDiff !== 0) return powerDiff;
            return (a.name || '').localeCompare(b.name || '');
          } else {
            const deadsGainDiff = (b.deadsGain || 0) - (a.deadsGain || 0);
            if (deadsGainDiff !== 0) return deadsGainDiff;
            const powerGainDiff = (b.powerGain || 0) - (a.powerGain || 0);
            if (powerGainDiff !== 0) return powerGainDiff;
            return (a.name || '').localeCompare(b.name || '');
          }
        });

      case 'underperforming':
        // Players with zero KP delta (no change in kill points)
        return sortedPlayers
          .filter(p => (p.killPoints || 0) === 0)
          .sort((a, b) => (b.power || 0) - (a.power || 0)); // Sort by power for secondary ranking

      default:
        return sortedPlayers.sort((a, b) => (b.killPoints || 0) - (a.killPoints || 0));
    }
  }, [players, activeTab, isBaselineOnly]);

  // Helper function to get tab label
  const getTabLabel = (tab: string, baseline: boolean) => {
    switch (tab) {
      case 'killpoints': return baseline ? 'Kill Points' : 'KP Gains';
      case 'power': return baseline ? 'Power' : 'Power Gains';
      case 'powerLoss': return 'Power Losses';
      case 'deads': return baseline ? 'Dead Troops' : 'Deads Gained';
      case 'underperforming': return 'Underperforming';
      default: return 'Rankings';
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Metrics Bar */}
      <EnhancedMetricsBar
        players={players}
        activeTab={activeTab}
        onTabChange={(tab) => setActiveTab(tab as typeof activeTab)}
        summaryStats={summaryStats}
        isBaselineOnly={isBaselineOnly}
      />

      {/* Player Rankings Table */}
      <div className={`rounded-2xl overflow-hidden shadow-xl ${
        theme === 'light' ? 'bg-white' : 'bg-gray-800'
      }`}>
        <div className="p-6">
          <KvKPlayerTable
            players={processedPlayers}
            title={`${getTabLabel(activeTab, isBaselineOnly)} Rankings`}
            mode={isBaselineOnly ? 'current' : 'gains'}
            showPowerLoss={activeTab === 'powerLoss'}
            pageSize={10000} // Show all players on one page for KvK dashboard
          />
        </div>
      </div>
    </div>
  );
};

export default KvKPerformanceDashboard;
