from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
from datetime import datetime

import models, schemas

# KvK CRUD operations
def get_kvk(db: Session, kvk_id: int):
    """Get a specific KvK by ID."""
    return db.query(models.KvK).filter(models.KvK.id == kvk_id).first()

def get_kvk_by_name(db: Session, name: str):
    """Get a KvK by name."""
    return db.query(models.KvK).filter(models.KvK.name == name).first()

def get_kvks(db: Session, skip: int = 0, limit: int = 100):
    """Get all KvKs, ordered by most recent first."""
    return db.query(models.KvK).order_by(desc(models.KvK.created_at)).offset(skip).limit(limit).all()

def get_active_kvk(db: Session):
    """Get the currently active KvK."""
    return db.query(models.KvK).filter(models.KvK.status == "active").first()

def create_kvk(db: Session, kvk: schemas.KvKCreate):
    """Create a new KvK."""
    # Check if name already exists
    existing_kvk = get_kvk_by_name(db, kvk.name)
    if existing_kvk:
        raise ValueError(f"KvK with name '{kvk.name}' already exists")

    # If no active KvK exists and this is being created as 'upcoming', make it 'active'
    active_kvk = get_active_kvk(db)
    status = kvk.status
    if not active_kvk and status == "upcoming":
        status = "active"

    db_kvk = models.KvK(
        name=kvk.name,
        start_date=kvk.start_date,
        end_date=kvk.end_date,
        status=status,
        season=kvk.season
    )
    db.add(db_kvk)
    db.commit()
    db.refresh(db_kvk)
    return db_kvk

def update_kvk(db: Session, kvk_id: int, kvk_update: schemas.KvKUpdate):
    """Update an existing KvK."""
    db_kvk = get_kvk(db, kvk_id)
    if not db_kvk:
        return None

    update_data = kvk_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_kvk, field, value)

    db.commit()
    db.refresh(db_kvk)
    return db_kvk

def delete_kvk(db: Session, kvk_id: int):
    """Delete a KvK."""
    db_kvk = get_kvk(db, kvk_id)
    if not db_kvk:
        return None

    db.delete(db_kvk)
    db.commit()
    return db_kvk

def update_kvk_status(db: Session, kvk_id: int, status: str, end_date: Optional[datetime] = None):
    """Update KvK status and optionally set end date."""
    db_kvk = get_kvk(db, kvk_id)
    if not db_kvk:
        return None

    db_kvk.status = status
    if end_date:
        db_kvk.end_date = end_date

    db.commit()
    db.refresh(db_kvk)
    return db_kvk

def get_kvk_scans(db: Session, kvk_id: int):
    """Get all scans for a specific KvK."""
    return db.query(models.Scan).filter(models.Scan.kvk_id == kvk_id).order_by(desc(models.Scan.timestamp)).all()