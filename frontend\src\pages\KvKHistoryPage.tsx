import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { useQuery } from '@tanstack/react-query';
import { getKvKList, getKvKPerformanceSummary } from '../api/api';
import { formatLargeNumber, formatDate } from '../utils/formatters';
import {
  FaHistory,
  FaSearch,
  FaPlus,
  FaEye,
  FaCrosshairs,
  FaSkullCrossbones,
  FaTrophy,
  FaUsers,
  FaCalendarAlt,
  FaShieldAlt,
  FaExclamationTriangle,
  FaFire,
  FaChartLine
} from 'react-icons/fa';

// Enhanced KvK Card Component with live data
const KvKCard: React.FC<{ kvk: any; theme: string }> = ({ kvk, theme }) => {
  // Fetch live performance data for this KvK
  const { data: performanceData } = useQuery({
    queryKey: ['kvkPerformance', kvk.id],
    queryFn: () => getKvKPerformanceSummary(kvk.id, 1000),
    enabled: !!kvk.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const stats = performanceData?.summary_stats || {
    total_kp_gain: 0,
    total_dead_troops: 0,
    total_players: 0,
    total_power_loss: 0,
    power_loss_players: 0
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-300';
      case 'upcoming':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <FaFire className="text-xs" />;
      case 'completed':
        return <FaTrophy className="text-xs" />;
      case 'upcoming':
        return <FaCalendarAlt className="text-xs" />;
      default:
        return null;
    }
  };

  return (
    <div
      className={`group relative overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:scale-102 hover:shadow-2xl ${
        theme === 'light' ? 'bg-white' : 'bg-gray-800'
      }`}
    >
      {/* Status Banner */}
      <div className={`px-4 py-3 ${
        kvk.status === 'active'
          ? 'bg-gradient-to-r from-green-500 to-emerald-500'
          : kvk.status === 'completed'
          ? 'bg-gradient-to-r from-blue-500 to-indigo-500'
          : 'bg-gradient-to-r from-yellow-500 to-orange-500'
      }`}>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-bold text-white text-lg">
              {kvk.name}
            </h3>
            <p className="text-white/80 text-sm">
              Season {kvk.season}
            </p>
          </div>
          <span className={`px-3 py-1 text-xs rounded-full font-semibold flex items-center space-x-1 ${getStatusColor(kvk.status)}`}>
            {getStatusIcon(kvk.status)}
            <span>{kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}</span>
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Date Information */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <p className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              Start Date
            </p>
            <p className={`font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
              {formatDate(kvk.startDate)}
            </p>
          </div>
          <div>
            <p className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              End Date
            </p>
            <p className={`font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
              {kvk.endDate ? formatDate(kvk.endDate) : 'Ongoing'}
            </p>
          </div>
        </div>

        {/* Live Statistics */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20">
            <div className="flex items-center space-x-2">
              <FaCrosshairs className="text-red-500" />
              <span className={`text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Kill Points
              </span>
            </div>
            <span className={`font-bold ${theme === 'light' ? 'text-red-700' : 'text-red-400'}`}>
              {formatLargeNumber(stats.total_kp_gain || 0)}
            </span>
          </div>

          <div className="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
            <div className="flex items-center space-x-2">
              <FaSkullCrossbones className="text-orange-500" />
              <span className={`text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Dead Troops
              </span>
            </div>
            <span className={`font-bold ${theme === 'light' ? 'text-orange-700' : 'text-orange-400'}`}>
              {formatLargeNumber(stats.total_dead_troops || 0)}
            </span>
          </div>

          <div className="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex items-center space-x-2">
              <FaUsers className="text-blue-500" />
              <span className={`text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Active Players
              </span>
            </div>
            <span className={`font-bold ${theme === 'light' ? 'text-blue-700' : 'text-blue-400'}`}>
              {stats.total_players || 0}
            </span>
          </div>

          {stats.power_loss_players > 0 && (
            <div className="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
              <div className="flex items-center space-x-2">
                <FaShieldAlt className="text-purple-500" />
                <span className={`text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                  Zeroed Players
                </span>
              </div>
              <span className={`font-bold ${theme === 'light' ? 'text-purple-700' : 'text-purple-400'}`}>
                {stats.power_loss_players}
              </span>
            </div>
          )}
        </div>

        {/* Action Button */}
        <Link
          to={`/kvk/${kvk.id}`}
          className={`block w-full text-center px-4 py-3 rounded-xl font-semibold transition-all duration-200 ${
            theme === 'light'
              ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700'
              : 'bg-gradient-to-r from-blue-700 to-indigo-700 text-white hover:from-blue-600 hover:to-indigo-600'
          } transform hover:scale-105`}
        >
          <div className="flex items-center justify-center space-x-2">
            <FaChartLine />
            <span>View Dashboard</span>
          </div>
        </Link>
      </div>
    </div>
  );
};

const KvKHistoryPage: React.FC = () => {
  const { theme } = useTheme();
  const { canManageKvK } = useUser();
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch KvK data
  const { data: kvkList = [], isLoading: isLoadingKvK } = useQuery({
    queryKey: ['kvkList'],
    queryFn: getKvKList
  });



  // Include all KvKs (active, completed, and upcoming)
  const allKvKs = kvkList;

  // Filter KvKs based on search term
  const filteredKvKs = allKvKs.filter(kvk =>
    kvk.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kvk.season.toString().includes(searchTerm)
  );

  // Sort KvKs by status priority (active first, then upcoming, then completed) and season
  const sortedKvKs = [...filteredKvKs].sort((a, b) => {
    // Status priority: active > upcoming > completed
    const statusPriority = { active: 3, upcoming: 2, completed: 1 };
    const aPriority = statusPriority[a.status as keyof typeof statusPriority] || 0;
    const bPriority = statusPriority[b.status as keyof typeof statusPriority] || 0;

    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }

    // If same status, sort by season (newest first)
    return b.season - a.season;
  });



  // Loading state
  if (isLoadingKvK) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`text-center ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
          Loading KvK history...
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaHistory className="text-2xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  KvK History
                </h1>
                <p className={`text-lg mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Complete archive of Kingdom 2358's battle history and achievements
                </p>
              </div>
            </div>

            {/* Stats Summary */}
            <div className="flex items-center space-x-6 mt-6">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  theme === 'light' ? 'bg-blue-500' : 'bg-blue-400'
                }`}></div>
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.length} Total KvKs
                </span>
              </div>
              <div className="flex items-center">
                <FaFire className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-green-600' : 'text-green-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.filter(kvk => kvk.status === 'active').length} Active
                </span>
              </div>
              <div className="flex items-center">
                <FaCalendarAlt className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.filter(kvk => kvk.status === 'upcoming').length} Upcoming
                </span>
              </div>
              <div className="flex items-center">
                <FaTrophy className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.filter(kvk => kvk.status === 'completed').length} Completed
                </span>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
            <FaHistory className="w-full h-full transform rotate-12" />
          </div>
        </div>

      {/* Search and Controls */}
      <div className="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="w-full md:w-1/3">
          <input
            type="text"
            placeholder="Search KvK by name or season..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full px-4 py-2 rounded-md border ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-700 text-white'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {canManageKvK() && (
          <Link
            to="/create-kvk"
            className={`px-4 py-2 rounded-md ${
              theme === 'light'
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-blue-700 text-white hover:bg-blue-600'
            }`}
          >
            Create New KvK
          </Link>
        )}
      </div>

      {/* Enhanced KvK Cards with Live Data */}
      {sortedKvKs.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sortedKvKs.map(kvk => (
            <KvKCard key={kvk.id} kvk={kvk} theme={theme} />
          ))}
        </div>
      ) : (
        <div className={`p-8 rounded-lg border text-center ${
          theme === 'light' ? 'bg-gray-50 border-gray-200 text-gray-500' : 'bg-gray-800 border-gray-700 text-gray-400'
        }`}>
          {searchTerm ? 'No KvKs match your search criteria.' : 'No KvKs available yet.'}
        </div>
      )}
      </div>
    </div>
  );
};

export default KvKHistoryPage;
