from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import re
import html
from typing import Optional, Dict, Any
import time
from collections import defaultdict
import ipaddress

from logging_config import get_logger
from database import get_db
import auth

logger = get_logger("security")

# Rate limiting storage (in production, use Redis)
rate_limit_storage = defaultdict(list)

# Security configuration with environment variable support
import os

SECURITY_CONFIG = {
    "max_requests_per_minute": int(os.getenv("MAX_REQUESTS_PER_MINUTE", "60")),
    "max_login_attempts_per_hour": int(os.getenv("MAX_LOGIN_ATTEMPTS_PER_HOUR", "5")),
    "max_upload_size_mb": int(os.getenv("MAX_UPLOAD_SIZE_MB", "100")),
    "allowed_file_extensions": os.getenv("ALLOWED_FILE_EXTENSIONS", ".xlsx,.xls,.csv").split(","),
    "blocked_ips": set(os.getenv("BLOCKED_IPS", "").split(",") if os.getenv("BLOCKED_IPS") else []),
    "trusted_proxies": set(os.getenv("TRUSTED_PROXIES", "127.0.0.1,::1").split(","))
}

class SecurityHeaders:
    """Add security headers to responses."""

    @staticmethod
    def get_headers() -> Dict[str, str]:
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';"
        }

def sanitize_input(value: str, max_length: int = 1000) -> str:
    """Sanitize user input to prevent XSS and other attacks."""
    if not isinstance(value, str):
        return str(value)

    # Limit length
    if len(value) > max_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Input too long. Maximum {max_length} characters allowed."
        )

    # HTML escape
    sanitized = html.escape(value)

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\']', '', sanitized)

    # Remove SQL injection patterns
    sql_patterns = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
        r'(--|#|/\*|\*/)',
        r'(\bOR\b.*=.*\bOR\b)',
        r'(\bAND\b.*=.*\bAND\b)'
    ]

    for pattern in sql_patterns:
        if re.search(pattern, sanitized, re.IGNORECASE):
            logger.warning(f"Potential SQL injection attempt detected: {value[:100]}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid input detected"
            )

    return sanitized.strip()

def validate_file_upload(filename: str, file_size: int) -> None:
    """Validate uploaded files."""
    # Check file extension
    if not any(filename.lower().endswith(ext) for ext in SECURITY_CONFIG["allowed_file_extensions"]):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not allowed. Allowed types: {', '.join(SECURITY_CONFIG['allowed_file_extensions'])}"
        )

    # Check file size
    max_size = SECURITY_CONFIG["max_upload_size_mb"] * 1024 * 1024
    if file_size > max_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size: {SECURITY_CONFIG['max_upload_size_mb']}MB"
        )

    # Check for suspicious filenames
    suspicious_patterns = [
        r'\.\./',  # Path traversal
        r'[<>:"|?*]',  # Invalid filename characters
        r'^\.',  # Hidden files
        r'\.exe$|\.bat$|\.cmd$|\.scr$|\.pif$'  # Executable files
    ]

    for pattern in suspicious_patterns:
        if re.search(pattern, filename, re.IGNORECASE):
            logger.warning(f"Suspicious filename detected: {filename}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid filename"
            )

def get_client_ip(request: Request) -> str:
    """Get the real client IP address, considering proxies."""
    # Check for forwarded headers (be careful with these in production)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain
        client_ip = forwarded_for.split(",")[0].strip()
        try:
            ipaddress.ip_address(client_ip)
            return client_ip
        except ValueError:
            pass

    # Check other common headers
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        try:
            ipaddress.ip_address(real_ip)
            return real_ip
        except ValueError:
            pass

    # Fall back to direct connection
    if request.client:
        return request.client.host

    return "unknown"

def check_rate_limit(client_ip: str, endpoint: str = "general") -> None:
    """Check if client has exceeded rate limits."""
    current_time = time.time()
    key = f"{client_ip}:{endpoint}"

    # Clean old entries (older than 1 hour)
    rate_limit_storage[key] = [
        timestamp for timestamp in rate_limit_storage[key]
        if current_time - timestamp < 3600
    ]

    # Check rate limit
    recent_requests = [
        timestamp for timestamp in rate_limit_storage[key]
        if current_time - timestamp < 60  # Last minute
    ]

    max_requests = SECURITY_CONFIG["max_requests_per_minute"]
    if endpoint == "login":
        max_requests = SECURITY_CONFIG["max_login_attempts_per_hour"]
        recent_requests = [
            timestamp for timestamp in rate_limit_storage[key]
            if current_time - timestamp < 3600  # Last hour for login attempts
        ]

    if len(recent_requests) >= max_requests:
        logger.warning(f"Rate limit exceeded for {client_ip} on {endpoint}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded. Please try again later."
        )

    # Add current request
    rate_limit_storage[key].append(current_time)

def check_ip_blacklist(client_ip: str) -> None:
    """Check if IP is blacklisted."""
    if client_ip in SECURITY_CONFIG["blocked_ips"]:
        logger.warning(f"Blocked IP attempted access: {client_ip}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

# Dependency for rate limiting
async def rate_limit_dependency(request: Request):
    """FastAPI dependency for rate limiting."""
    client_ip = get_client_ip(request)
    check_ip_blacklist(client_ip)
    check_rate_limit(client_ip)

# Dependency for login rate limiting
async def login_rate_limit_dependency(request: Request):
    """FastAPI dependency for login rate limiting."""
    client_ip = get_client_ip(request)
    check_ip_blacklist(client_ip)
    check_rate_limit(client_ip, "login")

# Enhanced authentication dependency
security = HTTPBearer(auto_error=False)

async def get_current_user_secure(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
):
    """Enhanced authentication with security checks."""
    client_ip = get_client_ip(request)

    # Apply rate limiting
    check_rate_limit(client_ip)

    # Get token from Authorization header or cookies
    token = None
    if credentials:
        token = credentials.credentials
    else:
        token = request.cookies.get("access_token")

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )

    # Validate token and get user
    try:
        user = await auth.get_current_user(token, db)

        # Log successful authentication
        logger.info(f"User {user.username} authenticated from {client_ip}")

        return user

    except HTTPException as e:
        # Log failed authentication
        logger.warning(f"Authentication failed from {client_ip}: {e.detail}")
        raise e
