/**
 * Data Consistency Service
 * Ensures data consistency across different pages and components
 */

import { useDataStore } from '../store/dataStore';

interface ConsistencyIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  pages: string[];
  field: string;
  expectedValue: any;
  actualValues: { page: string; value: any }[];
}

interface ConsistencyReport {
  isConsistent: boolean;
  issues: ConsistencyIssue[];
  lastChecked: Date;
  affectedPages: string[];
}

class DataConsistencyService {
  private store = useDataStore.getState();

  /**
   * Check consistency across all data sources
   */
  checkDataConsistency(): ConsistencyReport {
    const issues: ConsistencyIssue[] = [];
    const affectedPages: Set<string> = new Set();

    // Get current data from store
    const { dashboardData, performanceData, performance7DaysData, kvkList } = this.store;

    // Check player count consistency
    this.checkPlayerCountConsistency(dashboardData, performanceData, issues, affectedPages);

    // Check KP totals consistency
    this.checkKPTotalsConsistency(dashboardData, performanceData, issues, affectedPages);

    // Check power totals consistency
    this.checkPowerTotalsConsistency(dashboardData, performanceData, issues, affectedPages);

    // Check timeframe consistency
    this.checkTimeframeConsistency(dashboardData, performanceData, performance7DaysData, issues, affectedPages);

    // Check KvK status consistency
    this.checkKvKStatusConsistency(kvkList, issues, affectedPages);

    // Check scan timestamp consistency
    this.checkScanTimestampConsistency(dashboardData, performanceData, issues, affectedPages);

    return {
      isConsistent: issues.length === 0,
      issues,
      lastChecked: new Date(),
      affectedPages: Array.from(affectedPages)
    };
  }

  /**
   * Check player count consistency between dashboard and performance data
   */
  private checkPlayerCountConsistency(
    dashboardData: any,
    performanceData: any,
    issues: ConsistencyIssue[],
    affectedPages: Set<string>
  ): void {
    if (!dashboardData?.data || !performanceData?.data) return;

    const dashboardPlayerCount = dashboardData.data.player_count || 0;
    const performancePlayerCount = performanceData.data?.summary?.total_players || 
                                  performanceData.summary?.total_players || 0;

    // Allow for small differences (±5 players) due to timing differences
    const tolerance = 5;
    const difference = Math.abs(dashboardPlayerCount - performancePlayerCount);

    if (difference > tolerance) {
      issues.push({
        type: 'warning',
        message: `Player count mismatch between Dashboard and Performance pages`,
        pages: ['Dashboard', 'Performance'],
        field: 'player_count',
        expectedValue: dashboardPlayerCount,
        actualValues: [
          { page: 'Dashboard', value: dashboardPlayerCount },
          { page: 'Performance', value: performancePlayerCount }
        ]
      });
      affectedPages.add('Dashboard');
      affectedPages.add('Performance');
    }
  }

  /**
   * Check kill points totals consistency
   */
  private checkKPTotalsConsistency(
    dashboardData: any,
    performanceData: any,
    issues: ConsistencyIssue[],
    affectedPages: Set<string>
  ): void {
    if (!dashboardData?.data || !performanceData?.data) return;

    const dashboardKP = dashboardData.data.totals?.total_kill_points || 
                       dashboardData.data.total_kill_points || 0;
    const performanceKP = performanceData.data?.summary?.total_kp || 
                         performanceData.summary?.total_kp || 0;

    // Allow for small percentage difference due to calculation timing
    const tolerance = 0.05; // 5%
    const difference = Math.abs(dashboardKP - performanceKP);
    const percentageDiff = dashboardKP > 0 ? difference / dashboardKP : 0;

    if (percentageDiff > tolerance && difference > 1000000) { // Only flag if difference > 1M KP
      issues.push({
        type: 'warning',
        message: `Kill Points total mismatch between Dashboard and Performance pages`,
        pages: ['Dashboard', 'Performance'],
        field: 'total_kill_points',
        expectedValue: dashboardKP,
        actualValues: [
          { page: 'Dashboard', value: dashboardKP },
          { page: 'Performance', value: performanceKP }
        ]
      });
      affectedPages.add('Dashboard');
      affectedPages.add('Performance');
    }
  }

  /**
   * Check power totals consistency
   */
  private checkPowerTotalsConsistency(
    dashboardData: any,
    performanceData: any,
    issues: ConsistencyIssue[],
    affectedPages: Set<string>
  ): void {
    if (!dashboardData?.data || !performanceData?.data) return;

    const dashboardPower = dashboardData.data.totals?.total_power || 
                          dashboardData.data.total_power || 0;
    const performancePower = performanceData.data?.summary?.total_power || 
                            performanceData.summary?.total_power || 0;

    const tolerance = 0.05; // 5%
    const difference = Math.abs(dashboardPower - performancePower);
    const percentageDiff = dashboardPower > 0 ? difference / dashboardPower : 0;

    if (percentageDiff > tolerance && difference > 10000000) { // Only flag if difference > 10M power
      issues.push({
        type: 'warning',
        message: `Power total mismatch between Dashboard and Performance pages`,
        pages: ['Dashboard', 'Performance'],
        field: 'total_power',
        expectedValue: dashboardPower,
        actualValues: [
          { page: 'Dashboard', value: dashboardPower },
          { page: 'Performance', value: performancePower }
        ]
      });
      affectedPages.add('Dashboard');
      affectedPages.add('Performance');
    }
  }

  /**
   * Check timeframe consistency
   */
  private checkTimeframeConsistency(
    dashboardData: any,
    performanceData: any,
    performance7DaysData: any,
    issues: ConsistencyIssue[],
    affectedPages: Set<string>
  ): void {
    // Check if dashboard shows baseline-to-latest while performance shows 7-day data
    const dashboardTimeframe = dashboardData?.data?.timeframe || 'baseline_to_latest';
    const performanceTimeframe = performanceData?.data?.timeframe || 'baseline_to_latest';
    const performance7DaysTimeframe = performance7DaysData?.data?.timeframe || 'last_7_days';

    // Dashboard and general performance should use same timeframe
    if (dashboardTimeframe !== performanceTimeframe) {
      issues.push({
        type: 'info',
        message: `Different timeframes used: Dashboard (${dashboardTimeframe}) vs Performance (${performanceTimeframe})`,
        pages: ['Dashboard', 'Performance'],
        field: 'timeframe',
        expectedValue: dashboardTimeframe,
        actualValues: [
          { page: 'Dashboard', value: dashboardTimeframe },
          { page: 'Performance', value: performanceTimeframe }
        ]
      });
      affectedPages.add('Dashboard');
      affectedPages.add('Performance');
    }

    // Performance page should clearly distinguish between baseline-to-latest and 7-day data
    if (performance7DaysTimeframe !== 'last_7_days') {
      issues.push({
        type: 'warning',
        message: `7-day performance data has incorrect timeframe: ${performance7DaysTimeframe}`,
        pages: ['Performance'],
        field: 'timeframe_7days',
        expectedValue: 'last_7_days',
        actualValues: [
          { page: 'Performance (7-day)', value: performance7DaysTimeframe }
        ]
      });
      affectedPages.add('Performance');
    }
  }

  /**
   * Check KvK status consistency
   */
  private checkKvKStatusConsistency(
    kvkList: any[],
    issues: ConsistencyIssue[],
    affectedPages: Set<string>
  ): void {
    if (!kvkList || kvkList.length === 0) return;

    // Check for multiple active KvKs
    const activeKvKs = kvkList.filter(kvk => kvk.status === 'active');
    if (activeKvKs.length > 1) {
      issues.push({
        type: 'error',
        message: `Multiple active KvKs found: ${activeKvKs.length}`,
        pages: ['Dashboard', 'KvK History', 'KvK Dashboard'],
        field: 'kvk_status',
        expectedValue: 1,
        actualValues: [
          { page: 'All KvK pages', value: activeKvKs.length }
        ]
      });
      affectedPages.add('Dashboard');
      affectedPages.add('KvK History');
      affectedPages.add('KvK Dashboard');
    }

    // Check for KvKs with invalid status transitions
    kvkList.forEach(kvk => {
      const now = new Date();
      const startDate = new Date(kvk.startDate);
      const endDate = kvk.endDate ? new Date(kvk.endDate) : null;

      if (kvk.status === 'upcoming' && startDate < now) {
        issues.push({
          type: 'warning',
          message: `KvK "${kvk.name}" has status "upcoming" but start date has passed`,
          pages: ['KvK History', 'KvK Dashboard'],
          field: 'kvk_status_date_mismatch',
          expectedValue: 'active',
          actualValues: [
            { page: 'KvK pages', value: kvk.status }
          ]
        });
        affectedPages.add('KvK History');
        affectedPages.add('KvK Dashboard');
      }

      if (kvk.status === 'active' && endDate && endDate < now) {
        issues.push({
          type: 'warning',
          message: `KvK "${kvk.name}" has status "active" but end date has passed`,
          pages: ['KvK History', 'KvK Dashboard'],
          field: 'kvk_status_date_mismatch',
          expectedValue: 'completed',
          actualValues: [
            { page: 'KvK pages', value: kvk.status }
          ]
        });
        affectedPages.add('KvK History');
        affectedPages.add('KvK Dashboard');
      }
    });
  }

  /**
   * Check scan timestamp consistency
   */
  private checkScanTimestampConsistency(
    dashboardData: any,
    performanceData: any,
    issues: ConsistencyIssue[],
    affectedPages: Set<string>
  ): void {
    if (!dashboardData?.data?.scan_info || !performanceData?.data?.scan_info) return;

    const dashboardLatestScan = dashboardData.data.scan_info.latest_scan;
    const performanceLatestScan = performanceData.data.scan_info.latest_scan;

    if (dashboardLatestScan && performanceLatestScan) {
      const dashboardTimestamp = new Date(dashboardLatestScan.timestamp);
      const performanceTimestamp = new Date(performanceLatestScan.timestamp);
      
      // Allow for small time differences (5 minutes)
      const timeDiff = Math.abs(dashboardTimestamp.getTime() - performanceTimestamp.getTime());
      const fiveMinutes = 5 * 60 * 1000;

      if (timeDiff > fiveMinutes) {
        issues.push({
          type: 'warning',
          message: `Different latest scan timestamps between Dashboard and Performance`,
          pages: ['Dashboard', 'Performance'],
          field: 'latest_scan_timestamp',
          expectedValue: dashboardLatestScan.timestamp,
          actualValues: [
            { page: 'Dashboard', value: dashboardLatestScan.timestamp },
            { page: 'Performance', value: performanceLatestScan.timestamp }
          ]
        });
        affectedPages.add('Dashboard');
        affectedPages.add('Performance');
      }
    }
  }

  /**
   * Fix detected inconsistencies by refreshing data
   */
  async fixInconsistencies(report: ConsistencyReport): Promise<void> {
    console.log('[ConsistencyService] Fixing detected inconsistencies...');
    
    // Invalidate cache for affected data types
    const { refreshData } = this.store;
    
    if (report.affectedPages.includes('Dashboard')) {
      refreshData('dashboard');
    }
    
    if (report.affectedPages.includes('Performance')) {
      refreshData('performance');
    }
    
    if (report.affectedPages.includes('KvK History') || report.affectedPages.includes('KvK Dashboard')) {
      refreshData('kvk');
    }
    
    // Force refresh of all data if there are critical errors
    const criticalErrors = report.issues.filter(issue => issue.type === 'error');
    if (criticalErrors.length > 0) {
      console.log('[ConsistencyService] Critical errors detected, refreshing all data');
      refreshData('all');
    }
  }

  /**
   * Get consistency status for a specific page
   */
  getPageConsistencyStatus(pageName: string): {
    hasIssues: boolean;
    issues: ConsistencyIssue[];
    lastChecked: Date;
  } {
    const report = this.checkDataConsistency();
    const pageIssues = report.issues.filter(issue => 
      issue.pages.includes(pageName)
    );

    return {
      hasIssues: pageIssues.length > 0,
      issues: pageIssues,
      lastChecked: report.lastChecked
    };
  }

  /**
   * Schedule periodic consistency checks
   */
  startPeriodicChecks(intervalMs: number = 60000): () => void {
    console.log('[ConsistencyService] Starting periodic consistency checks');
    
    const interval = setInterval(() => {
      const report = this.checkDataConsistency();
      
      if (!report.isConsistent) {
        console.warn('[ConsistencyService] Data inconsistencies detected:', report.issues);
        
        // Auto-fix minor issues
        const minorIssues = report.issues.filter(issue => 
          issue.type === 'warning' || issue.type === 'info'
        );
        
        if (minorIssues.length > 0 && report.issues.length === minorIssues.length) {
          console.log('[ConsistencyService] Auto-fixing minor inconsistencies');
          this.fixInconsistencies(report);
        }
      }
    }, intervalMs);

    // Return cleanup function
    return () => {
      console.log('[ConsistencyService] Stopping periodic consistency checks');
      clearInterval(interval);
    };
  }
}

// Export singleton instance
export const consistencyService = new DataConsistencyService();

// Export hook for React components
export const useConsistencyService = () => {
  return {
    checkDataConsistency: consistencyService.checkDataConsistency.bind(consistencyService),
    fixInconsistencies: consistencyService.fixInconsistencies.bind(consistencyService),
    getPageConsistencyStatus: consistencyService.getPageConsistencyStatus.bind(consistencyService),
    startPeriodicChecks: consistencyService.startPeriodicChecks.bind(consistencyService),
  };
};
