"""
Advanced Analytics Engine
Provides comprehensive analytics, reporting, and business intelligence features.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

@dataclass
class AnalyticsMetric:
    """Analytics metric data structure."""
    name: str
    value: float
    unit: str
    trend: str  # 'up', 'down', 'stable'
    change_percent: float
    period: str
    timestamp: datetime

@dataclass
class PlayerInsight:
    """Player performance insight."""
    governor_id: str
    player_name: str
    insight_type: str  # 'top_performer', 'improving', 'declining', 'at_risk'
    description: str
    metrics: Dict[str, float]
    recommendations: List[str]

class PerformanceAnalyzer:
    """Advanced performance analysis engine."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def analyze_kingdom_trends(self, days: int = 30) -> Dict[str, Any]:
        """Analyze kingdom-wide performance trends."""
        
        # Get data for trend analysis
        query = text("""
            SELECT 
                s.timestamp,
                COUNT(ps.player_id) as player_count,
                SUM(ps.power) as total_power,
                SUM(ps.total_kill_points) as total_kp,
                SUM(ps.dead_troops) as total_dead,
                AVG(ps.power) as avg_power,
                AVG(ps.total_kill_points) as avg_kp
            FROM scans s
            JOIN player_stats ps ON s.id = ps.scan_id
            WHERE s.timestamp >= DATE('now', '-{} days')
            GROUP BY s.id, s.timestamp
            ORDER BY s.timestamp
        """.format(days))
        
        result = self.db.execute(query)
        data = [dict(row) for row in result]
        
        if not data:
            return {'error': 'No data available for trend analysis'}
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Calculate trends
        trends = {}
        for metric in ['total_power', 'total_kp', 'total_dead', 'player_count']:
            if len(df) > 1:
                trend_slope = np.polyfit(range(len(df)), df[metric], 1)[0]
                trends[metric] = {
                    'slope': float(trend_slope),
                    'direction': 'up' if trend_slope > 0 else 'down' if trend_slope < 0 else 'stable',
                    'current_value': float(df[metric].iloc[-1]),
                    'change_from_start': float(df[metric].iloc[-1] - df[metric].iloc[0]),
                    'percent_change': float((df[metric].iloc[-1] - df[metric].iloc[0]) / df[metric].iloc[0] * 100) if df[metric].iloc[0] != 0 else 0
                }
        
        return {
            'period_days': days,
            'data_points': len(df),
            'trends': trends,
            'summary': {
                'strongest_growth': max(trends.keys(), key=lambda k: trends[k]['percent_change']),
                'weakest_performance': min(trends.keys(), key=lambda k: trends[k]['percent_change']),
                'overall_health': self._calculate_kingdom_health(trends)
            }
        }
    
    def analyze_player_performance_patterns(self, limit: int = 100) -> List[PlayerInsight]:
        """Analyze individual player performance patterns."""
        
        # Get player performance data
        query = text("""
            SELECT 
                p.governor_id,
                p.name as player_name,
                p.alliance,
                ds.power_delta,
                ds.kill_points_delta,
                ds.dead_troops_delta,
                ds.is_zeroed,
                current_stats.power as current_power,
                current_stats.total_kill_points as current_kp,
                baseline_stats.power as baseline_power,
                baseline_stats.total_kill_points as baseline_kp
            FROM players p
            JOIN delta_stats ds ON p.id = ds.player_id
            JOIN player_stats current_stats ON ds.end_scan_id = current_stats.scan_id AND p.id = current_stats.player_id
            LEFT JOIN player_stats baseline_stats ON ds.start_scan_id = baseline_stats.scan_id AND p.id = baseline_stats.player_id
            ORDER BY ds.kill_points_delta DESC
            LIMIT :limit
        """)
        
        result = self.db.execute(query, {'limit': limit})
        players_data = [dict(row) for row in result]
        
        insights = []
        
        for player in players_data:
            insight = self._analyze_individual_player(player)
            if insight:
                insights.append(insight)
        
        return insights
    
    def _analyze_individual_player(self, player_data: Dict) -> Optional[PlayerInsight]:
        """Analyze individual player performance."""
        governor_id = player_data['governor_id']
        player_name = player_data['player_name']
        
        # Calculate performance metrics
        power_delta = player_data['power_delta'] or 0
        kp_delta = player_data['kill_points_delta'] or 0
        dead_delta = player_data['dead_troops_delta'] or 0
        current_power = player_data['current_power'] or 0
        
        metrics = {
            'power_delta': power_delta,
            'kp_delta': kp_delta,
            'dead_delta': dead_delta,
            'current_power': current_power
        }
        
        # Determine insight type and recommendations
        if player_data['is_zeroed']:
            return PlayerInsight(
                governor_id=governor_id,
                player_name=player_name,
                insight_type='at_risk',
                description=f"{player_name} has been zeroed and needs support",
                metrics=metrics,
                recommendations=[
                    "Provide resource assistance",
                    "Offer protection during rebuild",
                    "Check if player needs strategic guidance"
                ]
            )
        
        elif kp_delta > 50000000:  # 50M+ KP gain
            return PlayerInsight(
                governor_id=governor_id,
                player_name=player_name,
                insight_type='top_performer',
                description=f"{player_name} is a top performer with {kp_delta:,.0f} KP gained",
                metrics=metrics,
                recommendations=[
                    "Recognize outstanding performance",
                    "Consider for leadership roles",
                    "Share strategies with other players"
                ]
            )
        
        elif power_delta > 5000000 and kp_delta > 10000000:  # Good growth
            return PlayerInsight(
                governor_id=governor_id,
                player_name=player_name,
                insight_type='improving',
                description=f"{player_name} shows strong improvement in both power and combat",
                metrics=metrics,
                recommendations=[
                    "Continue current strategy",
                    "Consider advanced training",
                    "Monitor for continued growth"
                ]
            )
        
        elif kp_delta == 0 and power_delta < 1000000:  # Inactive/declining
            return PlayerInsight(
                governor_id=governor_id,
                player_name=player_name,
                insight_type='declining',
                description=f"{player_name} shows signs of inactivity or decline",
                metrics=metrics,
                recommendations=[
                    "Check player engagement",
                    "Offer assistance or guidance",
                    "Consider activity requirements"
                ]
            )
        
        return None
    
    def _calculate_kingdom_health(self, trends: Dict) -> str:
        """Calculate overall kingdom health score."""
        positive_trends = sum(1 for trend in trends.values() if trend['direction'] == 'up')
        total_trends = len(trends)
        
        health_score = positive_trends / total_trends if total_trends > 0 else 0
        
        if health_score >= 0.75:
            return 'excellent'
        elif health_score >= 0.5:
            return 'good'
        elif health_score >= 0.25:
            return 'fair'
        else:
            return 'poor'

class ReportGenerator:
    """Advanced report generation system."""
    
    def __init__(self, db: Session):
        self.db = db
        self.analyzer = PerformanceAnalyzer(db)
    
    def generate_executive_summary(self) -> Dict[str, Any]:
        """Generate executive summary report."""
        
        # Get latest scan data
        latest_scan_query = text("""
            SELECT 
                s.id,
                s.timestamp,
                COUNT(ps.player_id) as total_players,
                SUM(ps.power) as total_power,
                SUM(ps.total_kill_points) as total_kp,
                SUM(ps.dead_troops) as total_dead,
                AVG(ps.power) as avg_power
            FROM scans s
            JOIN player_stats ps ON s.id = ps.scan_id
            WHERE s.timestamp = (SELECT MAX(timestamp) FROM scans)
            GROUP BY s.id, s.timestamp
        """)
        
        result = self.db.execute(latest_scan_query).first()
        
        if not result:
            return {'error': 'No scan data available'}
        
        # Get trends
        trends = self.analyzer.analyze_kingdom_trends(30)
        
        # Get top performers
        top_performers_query = text("""
            SELECT 
                p.name,
                p.alliance,
                ds.kill_points_delta,
                ds.power_delta
            FROM players p
            JOIN delta_stats ds ON p.id = ds.player_id
            ORDER BY ds.kill_points_delta DESC
            LIMIT 5
        """)
        
        top_performers = [dict(row) for row in self.db.execute(top_performers_query)]
        
        # Get alliance breakdown
        alliance_query = text("""
            SELECT 
                p.alliance,
                COUNT(*) as member_count,
                SUM(ps.power) as total_power,
                SUM(ps.total_kill_points) as total_kp,
                AVG(ps.power) as avg_power
            FROM players p
            JOIN player_stats ps ON p.id = ps.player_id
            JOIN scans s ON ps.scan_id = s.id
            WHERE s.timestamp = (SELECT MAX(timestamp) FROM scans)
            GROUP BY p.alliance
            ORDER BY total_power DESC
        """)
        
        alliances = [dict(row) for row in self.db.execute(alliance_query)]
        
        return {
            'report_date': datetime.utcnow().isoformat(),
            'kingdom_overview': {
                'total_players': result.total_players,
                'total_power': result.total_power,
                'total_kill_points': result.total_kp,
                'average_power': result.avg_power,
                'last_scan_date': result.timestamp
            },
            'performance_trends': trends,
            'top_performers': top_performers,
            'alliance_breakdown': alliances[:10],  # Top 10 alliances
            'recommendations': self._generate_kingdom_recommendations(trends, alliances)
        }
    
    def generate_player_report(self, governor_id: str) -> Dict[str, Any]:
        """Generate detailed player performance report."""
        
        # Get player basic info
        player_query = text("""
            SELECT p.*, ps.power, ps.total_kill_points, ps.dead_troops
            FROM players p
            JOIN player_stats ps ON p.id = ps.player_id
            JOIN scans s ON ps.scan_id = s.id
            WHERE p.governor_id = :governor_id
            AND s.timestamp = (SELECT MAX(timestamp) FROM scans)
        """)
        
        player = self.db.execute(player_query, {'governor_id': governor_id}).first()
        
        if not player:
            return {'error': 'Player not found'}
        
        # Get performance history
        history_query = text("""
            SELECT 
                s.timestamp,
                ps.power,
                ps.total_kill_points,
                ps.dead_troops
            FROM player_stats ps
            JOIN scans s ON ps.scan_id = s.id
            JOIN players p ON ps.player_id = p.id
            WHERE p.governor_id = :governor_id
            ORDER BY s.timestamp
        """)
        
        history = [dict(row) for row in self.db.execute(history_query, {'governor_id': governor_id})]
        
        # Calculate player-specific metrics
        if len(history) > 1:
            latest = history[-1]
            previous = history[-2] if len(history) > 1 else history[0]
            
            metrics = {
                'power_change': latest['power'] - previous['power'],
                'kp_change': latest['total_kill_points'] - previous['total_kill_points'],
                'dead_change': latest['dead_troops'] - previous['dead_troops'],
                'growth_rate': (latest['power'] - previous['power']) / previous['power'] * 100 if previous['power'] > 0 else 0
            }
        else:
            metrics = {'power_change': 0, 'kp_change': 0, 'dead_change': 0, 'growth_rate': 0}
        
        return {
            'player_info': dict(player),
            'performance_metrics': metrics,
            'performance_history': history,
            'recommendations': self._generate_player_recommendations(metrics, dict(player))
        }
    
    def _generate_kingdom_recommendations(self, trends: Dict, alliances: List[Dict]) -> List[str]:
        """Generate kingdom-level recommendations."""
        recommendations = []
        
        if trends.get('trends', {}).get('total_power', {}).get('direction') == 'down':
            recommendations.append("Focus on power growth initiatives and resource management")
        
        if trends.get('trends', {}).get('total_kp', {}).get('direction') == 'down':
            recommendations.append("Increase combat training and KvK participation")
        
        if len(alliances) > 0:
            top_alliance_power = alliances[0]['total_power']
            if len(alliances) > 1:
                second_alliance_power = alliances[1]['total_power']
                if top_alliance_power > second_alliance_power * 2:
                    recommendations.append("Consider alliance balance and recruitment strategies")
        
        if not recommendations:
            recommendations.append("Kingdom performance is stable - maintain current strategies")
        
        return recommendations
    
    def _generate_player_recommendations(self, metrics: Dict, player_info: Dict) -> List[str]:
        """Generate player-specific recommendations."""
        recommendations = []
        
        if metrics['power_change'] < 0:
            recommendations.append("Focus on power recovery and resource protection")
        
        if metrics['kp_change'] == 0:
            recommendations.append("Increase combat participation and training")
        
        if metrics['growth_rate'] < 5:  # Less than 5% growth
            recommendations.append("Consider optimizing building and research priorities")
        
        if player_info['power'] < 10000000:  # Less than 10M power
            recommendations.append("Focus on early game development and resource gathering")
        
        if not recommendations:
            recommendations.append("Excellent performance - continue current strategy")
        
        return recommendations

class BusinessIntelligence:
    """Business intelligence and advanced analytics."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_engagement_metrics(self) -> Dict[str, Any]:
        """Calculate player engagement metrics."""
        
        # Active players (players with recent activity)
        active_query = text("""
            SELECT COUNT(DISTINCT p.id) as active_players
            FROM players p
            JOIN delta_stats ds ON p.id = ds.player_id
            WHERE ds.kill_points_delta > 0 OR ds.power_delta > 1000000
        """)
        
        active_players = self.db.execute(active_query).scalar()
        
        # Total players
        total_query = text("SELECT COUNT(*) FROM players")
        total_players = self.db.execute(total_query).scalar()
        
        # Engagement rate
        engagement_rate = (active_players / total_players * 100) if total_players > 0 else 0
        
        return {
            'total_players': total_players,
            'active_players': active_players,
            'engagement_rate': round(engagement_rate, 2),
            'inactive_players': total_players - active_players
        }
    
    def analyze_alliance_performance(self) -> List[Dict[str, Any]]:
        """Analyze alliance performance metrics."""
        
        query = text("""
            SELECT 
                p.alliance,
                COUNT(*) as member_count,
                SUM(ps.power) as total_power,
                AVG(ps.power) as avg_power,
                SUM(ps.total_kill_points) as total_kp,
                AVG(ps.total_kill_points) as avg_kp,
                SUM(CASE WHEN ds.is_zeroed THEN 1 ELSE 0 END) as zeroed_members,
                SUM(ds.power_delta) as total_power_gain,
                SUM(ds.kill_points_delta) as total_kp_gain
            FROM players p
            JOIN player_stats ps ON p.id = ps.player_id
            JOIN scans s ON ps.scan_id = s.id
            LEFT JOIN delta_stats ds ON p.id = ds.player_id
            WHERE s.timestamp = (SELECT MAX(timestamp) FROM scans)
            GROUP BY p.alliance
            HAVING COUNT(*) >= 5  -- Only alliances with 5+ members
            ORDER BY total_power DESC
        """)
        
        alliances = [dict(row) for row in self.db.execute(query)]
        
        # Calculate performance scores
        for alliance in alliances:
            # Performance score based on multiple factors
            power_score = min(alliance['avg_power'] / 50000000, 1) * 30  # Max 30 points for 50M+ avg power
            kp_score = min(alliance['avg_kp'] / 100000000, 1) * 30  # Max 30 points for 100M+ avg KP
            growth_score = min(alliance['total_power_gain'] / alliance['member_count'] / 10000000, 1) * 25 if alliance['total_power_gain'] else 0  # Max 25 points for 10M+ avg growth
            stability_score = max(0, (alliance['member_count'] - alliance['zeroed_members']) / alliance['member_count']) * 15  # Max 15 points for no zeroed members
            
            alliance['performance_score'] = round(power_score + kp_score + growth_score + stability_score, 1)
        
        return sorted(alliances, key=lambda x: x['performance_score'], reverse=True)

# Export main components
__all__ = [
    'PerformanceAnalyzer',
    'ReportGenerator', 
    'BusinessIntelligence',
    'AnalyticsMetric',
    'PlayerInsight'
]
