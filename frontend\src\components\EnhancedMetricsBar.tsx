import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { formatSmartNumber } from '../utils/formatters';
import {
  FaTrophy,
  FaFire,
  FaSkullCrossbones,
  FaShieldAlt,
  FaExclamation<PERSON><PERSON>gle,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa';

interface MetricButtonProps {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  count: number;
  color: 'blue' | 'green' | 'red' | 'purple' | 'orange';
  isActive: boolean;
  onClick: () => void;
  trend?: 'up' | 'down' | 'neutral';
}

interface EnhancedMetricsBarProps {
  players: any[];
  activeTab: string;
  onTabChange: (tab: string) => void;
  summaryStats?: {
    total_kp_gain: number;
    total_power_gain: number;
    total_power_loss: number;
    net_power_change: number;
    total_dead_troops: number;
    needs_improvement: number;
    power_loss_players: number;
  };
  isBaselineOnly?: boolean;
}

const MetricButton: React.FC<MetricButtonProps> = ({
  icon,
  label,
  value,
  count,
  color,
  isActive,
  onClick,
  trend
}) => {
  const { theme } = useTheme();

  const colorClasses = {
    blue: {
      bg: isActive ? 'bg-blue-600' : theme === 'light' ? 'bg-blue-50 hover:bg-blue-100' : 'bg-blue-900/20 hover:bg-blue-900/40',
      text: isActive ? 'text-white' : theme === 'light' ? 'text-blue-700' : 'text-blue-300',
      border: isActive ? 'border-blue-600' : 'border-blue-200',
      icon: isActive ? 'text-white' : 'text-blue-500'
    },
    green: {
      bg: isActive ? 'bg-green-600' : theme === 'light' ? 'bg-green-50 hover:bg-green-100' : 'bg-green-900/20 hover:bg-green-900/40',
      text: isActive ? 'text-white' : theme === 'light' ? 'text-green-700' : 'text-green-300',
      border: isActive ? 'border-green-600' : 'border-green-200',
      icon: isActive ? 'text-white' : 'text-green-500'
    },
    red: {
      bg: isActive ? 'bg-red-600' : theme === 'light' ? 'bg-red-50 hover:bg-red-100' : 'bg-red-900/20 hover:bg-red-900/40',
      text: isActive ? 'text-white' : theme === 'light' ? 'text-red-700' : 'text-red-300',
      border: isActive ? 'border-red-600' : 'border-red-200',
      icon: isActive ? 'text-white' : 'text-red-500'
    },
    purple: {
      bg: isActive ? 'bg-purple-600' : theme === 'light' ? 'bg-purple-50 hover:bg-purple-100' : 'bg-purple-900/20 hover:bg-purple-900/40',
      text: isActive ? 'text-white' : theme === 'light' ? 'text-purple-700' : 'text-purple-300',
      border: isActive ? 'border-purple-600' : 'border-purple-200',
      icon: isActive ? 'text-white' : 'text-purple-500'
    },
    orange: {
      bg: isActive ? 'bg-orange-600' : theme === 'light' ? 'bg-orange-50 hover:bg-orange-100' : 'bg-orange-900/20 hover:bg-orange-900/40',
      text: isActive ? 'text-white' : theme === 'light' ? 'text-orange-700' : 'text-orange-300',
      border: isActive ? 'border-orange-600' : 'border-orange-200',
      icon: isActive ? 'text-white' : 'text-orange-500'
    }
  };

  const classes = colorClasses[color];

  return (
    <button
      onClick={onClick}
      className={`relative flex flex-col items-center p-4 rounded-xl border-2 transition-all duration-200 transform hover:scale-105 ${classes.bg} ${classes.border} ${classes.text} shadow-lg hover:shadow-xl`}
    >
      {/* Icon and Trend */}
      <div className="flex items-center mb-2">
        <div className={`text-xl ${classes.icon}`}>
          {icon}
        </div>
        {trend && (
          <div className={`ml-2 text-xs ${
            trend === 'up' ? 'text-green-500' : trend === 'down' ? 'text-red-500' : 'text-gray-500'
          }`}>
            {trend === 'up' ? <FaArrowUp /> : trend === 'down' ? <FaArrowDown /> : null}
          </div>
        )}
      </div>

      {/* Label */}
      <div className="text-xs font-medium mb-1 text-center">
        {label}
      </div>

      {/* Value */}
      <div className="text-lg font-bold mb-1">
        {typeof value === 'number' ? formatSmartNumber(value, true) : value}
      </div>

      {/* Count Badge */}
      <div className={`px-2 py-1 text-xs rounded-full font-semibold ${
        isActive 
          ? 'bg-white/20 text-white' 
          : theme === 'light' 
            ? 'bg-gray-200 text-gray-700' 
            : 'bg-gray-700 text-gray-300'
      }`}>
        {count}
      </div>
    </button>
  );
};

const EnhancedMetricsBar: React.FC<EnhancedMetricsBarProps> = ({
  players,
  activeTab,
  onTabChange,
  summaryStats,
  isBaselineOnly = false
}) => {
  const { theme } = useTheme();

  // Calculate metrics
  const metrics = [
    {
      id: 'killpoints',
      icon: <FaTrophy />,
      label: isBaselineOnly ? 'Kill Points' : 'KP Gains',
      value: summaryStats?.total_kp_gain || 0,
      count: isBaselineOnly ? players.length : players.filter(p => (p.killPoints || 0) > 0).length,
      color: 'blue' as const,
      trend: 'up' as const
    },
    {
      id: 'power',
      icon: <FaFire />,
      label: isBaselineOnly ? 'Power' : 'Power Gains',
      value: summaryStats?.total_power_gain || 0,
      count: isBaselineOnly ? players.length : players.filter(p => (p.power || 0) > 0).length,
      color: 'green' as const,
      trend: 'up' as const
    },
    {
      id: 'powerLoss',
      icon: <FaShieldAlt />,
      label: 'Power Losses',
      value: Math.abs(summaryStats?.total_power_loss || 0),
      count: players.filter(p => (p.power || 0) < 0).length,
      color: 'red' as const,
      trend: 'down' as const,
      hidden: isBaselineOnly
    },
    {
      id: 'deads',
      icon: <FaSkullCrossbones />,
      label: isBaselineOnly ? 'Dead Troops' : 'Deads Gained',
      value: summaryStats?.total_dead_troops || 0,
      count: isBaselineOnly ? players.length : players.filter(p => (p.deads || 0) > 0).length,
      color: 'purple' as const,
      trend: 'neutral' as const
    },
    {
      id: 'underperforming',
      icon: <FaExclamationTriangle />,
      label: 'Underperforming',
      value: summaryStats?.needs_improvement || 0,
      count: players.filter(p => (p.killPoints || 0) === 0).length,
      color: 'orange' as const,
      trend: 'down' as const,
      hidden: isBaselineOnly
    }
  ].filter(metric => !metric.hidden);

  return (
    <div className={`p-6 rounded-2xl shadow-xl mb-6 ${
      theme === 'light' 
        ? 'bg-gradient-to-r from-gray-50 to-white border border-gray-200' 
        : 'bg-gradient-to-r from-gray-800 to-gray-900 border border-gray-700'
    }`}>
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {metrics.map((metric) => (
          <MetricButton
            key={metric.id}
            icon={metric.icon}
            label={metric.label}
            value={metric.value}
            count={metric.count}
            color={metric.color}
            isActive={activeTab === metric.id}
            onClick={() => onTabChange(metric.id)}
            trend={metric.trend}
          />
        ))}
      </div>
    </div>
  );
};

export default EnhancedMetricsBar;
