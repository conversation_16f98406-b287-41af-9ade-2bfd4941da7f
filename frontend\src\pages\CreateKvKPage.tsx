import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { KvKPhase } from '../types/dataTypes';
import { createKvK } from '../services/kvkService';
import {
  FaShieldAlt,
  FaCalendarAlt,
  FaHashtag,
  FaPlay,
  FaUpload,
  FaFileExcel,
  FaCheckCircle,
  FaExclamationTriangle
} from 'react-icons/fa';

interface FormData {
  name: string;
  season: number;
  startDate: string;
  kvkPhase: KvKPhase;
  baselineScan: File | null;
  baselineScanName: string;
}

const CreateKvKPage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize form data
  const [formData, setFormData] = useState<FormData>({
    name: '',
    season: 1,
    startDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
    kvkPhase: KvKPhase.PreKvK,
    baselineScan: null,
    baselineScanName: 'Baseline Scan'
  });

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'season' ? parseInt(value) : value
    }));
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setError(null);

    if (!file) {
      setFormData(prev => ({ ...prev, baselineScan: null }));
      return;
    }

    // Check file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const validExtensions = ['xlsx', 'xls', 'csv'];

    if (!fileExtension || !validExtensions.includes(fileExtension)) {
      setError(`Please upload a valid Excel or CSV file. Received file with extension: ${fileExtension || 'unknown'}`);
      setFormData(prev => ({ ...prev, baselineScan: null }));
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    // Set the file in state
    setFormData(prev => ({ ...prev, baselineScan: file }));
  };

  // Create KvK mutation
  const createKvKMutation = useMutation({
    mutationFn: async () => {
      try {
        // Validate form data
        if (!formData.name.trim()) {
          throw new Error('KvK name is required');
        }

        if (formData.season < 1) {
          throw new Error('Season must be a positive number');
        }

        // Create new KvK
        const newKvK = await createKvK(
          formData.name,
          formData.startDate,
          formData.season
        );

        // If a baseline scan was provided, upload it
        if (formData.baselineScan) {
          console.log('Uploading baseline scan for KvK:', newKvK.id);

          const uploadFormData = new FormData();
          uploadFormData.append('file', formData.baselineScan);

          const uploadUrl = `/api/scans/upload?scan_name=${encodeURIComponent(formData.baselineScanName)}&is_baseline=true&kvk_id=${newKvK.id}`;

          const uploadResponse = await fetch(uploadUrl, {
            method: 'POST',
            body: uploadFormData,
          });

          if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text();
            throw new Error(`Failed to upload baseline scan: ${errorText}`);
          }

          const scanData = await uploadResponse.json();
          console.log('Baseline scan uploaded successfully:', scanData);
        }

        return newKvK;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data) => {
      // Set success message based on whether baseline scan was uploaded
      const message = formData.baselineScan
        ? 'KvK created and baseline scan uploaded successfully!'
        : 'KvK created successfully!';
      setSuccess(message);

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['kvkList'] });
      queryClient.invalidateQueries({ queryKey: ['scansList'] });

      // Wait a moment to show the success message before redirecting
      setTimeout(() => {
        navigate(`/kvk/${data.id}`);
      }, 2000);
    },
    onError: (error: Error) => {
      setError(`Failed to create KvK: ${error.message}`);
    }
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    createKvKMutation.mutate();
  };

  return (
    <div className="max-w-5xl mx-auto px-4 py-8">
      {/* Enhanced Header */}
      <div className={`relative overflow-hidden rounded-2xl mb-8 ${
        theme === 'light'
          ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
          : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
      }`}>
        <div className="relative px-8 py-12">
          <div className="flex items-center mb-4">
            <div className={`p-3 rounded-xl mr-4 ${
              theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
            }`}>
              <FaShieldAlt className="text-2xl" />
            </div>
            <div>
              <h1 className={`text-4xl font-bold ${
                theme === 'light' ? 'text-gray-900' : 'text-white'
              }`}>
                Create New KvK
              </h1>
              <p className={`text-lg mt-2 ${
                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
              }`}>
                Set up a new Kingdom vs Kingdom event and upload a baseline scan
              </p>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center space-x-4 mt-6">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                theme === 'light' ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white'
              }`}>
                1
              </div>
              <span className={`ml-2 text-sm font-medium ${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              }`}>
                KvK Details
              </span>
            </div>
            <div className={`w-8 h-0.5 ${
              theme === 'light' ? 'bg-gray-300' : 'bg-gray-600'
            }`}></div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                theme === 'light' ? 'bg-gray-300 text-gray-600' : 'bg-gray-600 text-gray-400'
              }`}>
                2
              </div>
              <span className={`ml-2 text-sm font-medium ${
                theme === 'light' ? 'text-gray-500' : 'text-gray-500'
              }`}>
                Baseline Scan
              </span>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
          <FaShieldAlt className="w-full h-full transform rotate-12" />
        </div>
      </div>

      {/* Enhanced Success Message */}
      {success && (
        <div className={`mb-6 p-6 rounded-xl border-l-4 border-green-500 ${
          theme === 'light'
            ? 'bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200'
            : 'bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-700'
        }`}>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FaCheckCircle className={`h-6 w-6 ${
                theme === 'light' ? 'text-green-500' : 'text-green-400'
              }`} />
            </div>
            <div className="ml-4">
              <h3 className={`text-lg font-semibold ${
                theme === 'light' ? 'text-green-800' : 'text-green-300'
              }`}>
                Success!
              </h3>
              <div className={`mt-2 text-sm ${
                theme === 'light' ? 'text-green-700' : 'text-green-200'
              }`}>
                <p className="font-medium">{success}</p>
                <p className="mt-1 flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Redirecting to KvK page...
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Error Message */}
      {error && (
        <div className={`mb-6 p-6 rounded-xl border-l-4 border-red-500 ${
          theme === 'light'
            ? 'bg-gradient-to-r from-red-50 to-pink-50 border border-red-200'
            : 'bg-gradient-to-r from-red-900/20 to-pink-900/20 border border-red-700'
        }`}>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className={`h-6 w-6 ${
                theme === 'light' ? 'text-red-500' : 'text-red-400'
              }`} />
            </div>
            <div className="ml-4">
              <h3 className={`text-lg font-semibold ${
                theme === 'light' ? 'text-red-800' : 'text-red-300'
              }`}>
                Error
              </h3>
              <div className={`mt-2 text-sm ${
                theme === 'light' ? 'text-red-700' : 'text-red-200'
              }`}>
                <p className="font-medium">{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Form Container */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* KvK Details Card */}
        <div className={`p-8 rounded-2xl shadow-lg border ${
          theme === 'light'
            ? 'bg-white border-gray-200'
            : 'bg-gray-800 border-gray-700'
        }`}>
          <div className="flex items-center mb-6">
            <div className={`p-2 rounded-lg mr-3 ${
              theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
            }`}>
              <FaShieldAlt className="text-lg" />
            </div>
            <h2 className={`text-2xl font-bold ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              KvK Details
            </h2>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* KvK Name */}
              <div className="space-y-2">
                <label htmlFor="name" className={`flex items-center text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  <FaShieldAlt className="mr-2 text-blue-500" />
                  KvK Name
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder="e.g., Heroic Anthem"
                    className={`block w-full px-4 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                      theme === 'light'
                        ? 'bg-gray-50 border-gray-300 text-gray-900 hover:bg-white'
                        : 'bg-gray-700 border-gray-600 text-white hover:bg-gray-600'
                    }`}
                  />
                </div>
              </div>

              {/* Season */}
              <div className="space-y-2">
                <label htmlFor="season" className={`flex items-center text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  <FaHashtag className="mr-2 text-purple-500" />
                  Season Number
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <div className="relative">
                  <input
                    type="number"
                    id="season"
                    name="season"
                    value={formData.season}
                    onChange={handleInputChange}
                    required
                    min="1"
                    className={`block w-full px-4 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                      theme === 'light'
                        ? 'bg-gray-50 border-gray-300 text-gray-900 hover:bg-white'
                        : 'bg-gray-700 border-gray-600 text-white hover:bg-gray-600'
                    }`}
                  />
                </div>
              </div>

              {/* Start Date */}
              <div className="space-y-2">
                <label htmlFor="startDate" className={`flex items-center text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  <FaCalendarAlt className="mr-2 text-green-500" />
                  Start Date
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <div className="relative">
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    required
                    className={`block w-full px-4 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                      theme === 'light'
                        ? 'bg-gray-50 border-gray-300 text-gray-900 hover:bg-white'
                        : 'bg-gray-700 border-gray-600 text-white hover:bg-gray-600'
                    }`}
                  />
                </div>
              </div>

              {/* KvK Phase */}
              <div className="space-y-2">
                <label htmlFor="kvkPhase" className={`flex items-center text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  <FaPlay className="mr-2 text-orange-500" />
                  Initial Phase
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <div className="relative">
                  <select
                    id="kvkPhase"
                    name="kvkPhase"
                    value={formData.kvkPhase}
                    onChange={handleInputChange}
                    required
                    className={`block w-full px-4 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                      theme === 'light'
                        ? 'bg-gray-50 border-gray-300 text-gray-900 hover:bg-white'
                        : 'bg-gray-700 border-gray-600 text-white hover:bg-gray-600'
                    }`}
                  >
                    {Object.values(KvKPhase).map(phase => (
                      <option key={phase} value={phase}>{phase}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Baseline Scan Card */}
        <div className={`p-8 rounded-2xl shadow-lg border ${
          theme === 'light'
            ? 'bg-white border-gray-200'
            : 'bg-gray-800 border-gray-700'
        }`}>
          <div className="flex items-center mb-6">
            <div className={`p-2 rounded-lg mr-3 ${
              theme === 'light' ? 'bg-green-100 text-green-600' : 'bg-green-900 text-green-400'
            }`}>
              <FaUpload className="text-lg" />
            </div>
            <div>
              <h2 className={`text-2xl font-bold ${
                theme === 'light' ? 'text-gray-900' : 'text-white'
              }`}>
                Baseline Scan
              </h2>
              <p className={`text-sm ${
                theme === 'light' ? 'text-gray-600' : 'text-gray-400'
              }`}>
                Optional - Upload initial player data for comparison
              </p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Baseline Scan Name */}
            <div className="space-y-2">
              <label htmlFor="baselineScanName" className={`flex items-center text-sm font-semibold ${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              }`}>
                <FaFileExcel className="mr-2 text-green-500" />
                Scan Name
              </label>
              <input
                type="text"
                id="baselineScanName"
                name="baselineScanName"
                value={formData.baselineScanName}
                onChange={handleInputChange}
                placeholder="Baseline Scan"
                className={`block w-full px-4 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 ${
                  theme === 'light'
                    ? 'bg-gray-50 border-gray-300 text-gray-900 hover:bg-white'
                    : 'bg-gray-700 border-gray-600 text-white hover:bg-gray-600'
                }`}
              />
            </div>

            {/* Enhanced File Upload */}
            <div className="space-y-2">
              <label className={`flex items-center text-sm font-semibold ${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              }`}>
                <FaUpload className="mr-2 text-blue-500" />
                Upload Baseline Scan
              </label>

              <div className={`relative border-2 border-dashed rounded-2xl transition-all duration-300 hover:border-blue-400 ${
                formData.baselineScan
                  ? (theme === 'light' ? 'border-green-400 bg-green-50' : 'border-green-500 bg-green-900/20')
                  : (theme === 'light' ? 'border-gray-300 bg-gray-50 hover:bg-gray-100' : 'border-gray-600 bg-gray-900 hover:bg-gray-800')
              }`}>
                <div className="px-8 py-12 text-center">
                  {formData.baselineScan ? (
                    <div className="space-y-4">
                      <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center ${
                        theme === 'light' ? 'bg-green-100' : 'bg-green-900'
                      }`}>
                        <FaCheckCircle className={`text-2xl ${
                          theme === 'light' ? 'text-green-600' : 'text-green-400'
                        }`} />
                      </div>
                      <div>
                        <p className={`text-lg font-semibold ${
                          theme === 'light' ? 'text-green-800' : 'text-green-300'
                        }`}>
                          File Selected
                        </p>
                        <p className={`text-sm ${
                          theme === 'light' ? 'text-green-600' : 'text-green-400'
                        }`}>
                          {formData.baselineScan.name}
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          setFormData(prev => ({ ...prev, baselineScan: null }));
                          if (fileInputRef.current) fileInputRef.current.value = '';
                        }}
                        className={`text-sm font-medium ${
                          theme === 'light' ? 'text-red-600 hover:text-red-500' : 'text-red-400 hover:text-red-300'
                        }`}
                      >
                        Remove file
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center ${
                        theme === 'light' ? 'bg-blue-100' : 'bg-blue-900'
                      }`}>
                        <FaFileExcel className={`text-2xl ${
                          theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                        }`} />
                      </div>
                      <div>
                        <label
                          htmlFor="file-upload"
                          className={`cursor-pointer text-lg font-semibold ${
                            theme === 'light' ? 'text-blue-600 hover:text-blue-500' : 'text-blue-400 hover:text-blue-300'
                          }`}
                        >
                          Choose file or drag and drop
                        </label>
                        <p className={`text-sm mt-1 ${
                          theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                        }`}>
                          Excel (.xlsx, .xls) or CSV files only
                        </p>
                      </div>
                    </div>
                  )}

                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept=".xlsx,.xls,.csv"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={createKvKMutation.isPending}
            className={`group relative inline-flex items-center px-8 py-4 border border-transparent text-lg font-semibold rounded-2xl shadow-lg text-white transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-500/50 ${
              createKvKMutation.isPending
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl'
            }`}
          >
            {createKvKMutation.isPending ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating KvK...
              </>
            ) : (
              <>
                <FaShieldAlt className="mr-3 text-xl group-hover:animate-pulse" />
                Create KvK
                <div className="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateKvKPage;
