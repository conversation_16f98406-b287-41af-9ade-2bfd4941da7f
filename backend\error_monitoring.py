"""
Comprehensive error monitoring and alerting system.
"""

import logging
import traceback
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
import json
import os
from dataclasses import dataclass, asdict
from enum import Enum

from logging_config import get_logger

logger = get_logger("error_monitoring")

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorEvent:
    timestamp: datetime
    error_type: str
    message: str
    severity: ErrorSeverity
    module: str
    function: str
    traceback: str
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_context: Optional[Dict[str, Any]] = None

class ErrorMonitor:
    """Centralized error monitoring and alerting system."""
    
    def __init__(self, max_errors_per_window: int = 100, window_minutes: int = 60):
        self.max_errors_per_window = max_errors_per_window
        self.window_minutes = window_minutes
        self.error_history: deque = deque(maxlen=1000)  # Keep last 1000 errors
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.last_alert_time: Dict[str, datetime] = {}
        self.alert_cooldown_minutes = 15  # Minimum time between alerts for same error type
        
    def log_error(self, 
                  error: Exception, 
                  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                  module: str = "unknown",
                  function: str = "unknown",
                  user_id: Optional[str] = None,
                  request_id: Optional[str] = None,
                  additional_context: Optional[Dict[str, Any]] = None) -> None:
        """Log an error event with comprehensive details."""
        
        error_event = ErrorEvent(
            timestamp=datetime.utcnow(),
            error_type=type(error).__name__,
            message=str(error),
            severity=severity,
            module=module,
            function=function,
            traceback=traceback.format_exc(),
            user_id=user_id,
            request_id=request_id,
            additional_context=additional_context or {}
        )
        
        # Add to history
        self.error_history.append(error_event)
        
        # Update counts
        error_key = f"{error_event.error_type}:{error_event.module}:{error_event.function}"
        self.error_counts[error_key] += 1
        
        # Log to standard logger
        log_message = f"[{severity.value.upper()}] {error_event.error_type} in {module}.{function}: {error_event.message}"
        if user_id:
            log_message += f" (User: {user_id})"
        if request_id:
            log_message += f" (Request: {request_id})"
            
        if severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # Check if we need to send alerts
        self._check_alert_conditions(error_event, error_key)
        
        # Save to file for persistence
        self._save_error_to_file(error_event)
    
    def _check_alert_conditions(self, error_event: ErrorEvent, error_key: str) -> None:
        """Check if error conditions warrant an alert."""
        now = datetime.utcnow()
        
        # Check if we've already alerted for this error type recently
        if error_key in self.last_alert_time:
            time_since_last_alert = now - self.last_alert_time[error_key]
            if time_since_last_alert.total_seconds() < self.alert_cooldown_minutes * 60:
                return
        
        # Alert conditions
        should_alert = False
        alert_reason = ""
        
        # Critical errors always trigger alerts
        if error_event.severity == ErrorSeverity.CRITICAL:
            should_alert = True
            alert_reason = "Critical error detected"
        
        # High frequency of same error type
        elif self.error_counts[error_key] >= 5:
            should_alert = True
            alert_reason = f"High frequency error: {self.error_counts[error_key]} occurrences"
        
        # Too many errors in time window
        elif self._get_error_count_in_window() >= self.max_errors_per_window:
            should_alert = True
            alert_reason = f"Error rate exceeded: {self._get_error_count_in_window()} errors in {self.window_minutes} minutes"
        
        if should_alert:
            self._send_alert(error_event, alert_reason)
            self.last_alert_time[error_key] = now
    
    def _get_error_count_in_window(self) -> int:
        """Get count of errors in the current time window."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=self.window_minutes)
        return sum(1 for error in self.error_history if error.timestamp > cutoff_time)
    
    def _send_alert(self, error_event: ErrorEvent, reason: str) -> None:
        """Send alert notification (placeholder for actual implementation)."""
        alert_message = f"""
        🚨 ERROR ALERT 🚨
        
        Reason: {reason}
        Error: {error_event.error_type}
        Message: {error_event.message}
        Module: {error_event.module}.{error_event.function}
        Severity: {error_event.severity.value}
        Time: {error_event.timestamp.isoformat()}
        
        Traceback:
        {error_event.traceback}
        """
        
        # Log the alert
        logger.critical(f"ALERT TRIGGERED: {reason}")
        logger.critical(alert_message)
        
        # In production, you would send this to:
        # - Email notifications
        # - Slack/Discord webhooks
        # - SMS alerts
        # - Monitoring services (Sentry, DataDog, etc.)
    
    def _save_error_to_file(self, error_event: ErrorEvent) -> None:
        """Save error to file for persistence."""
        try:
            error_dir = "logs/errors"
            os.makedirs(error_dir, exist_ok=True)
            
            date_str = error_event.timestamp.strftime("%Y-%m-%d")
            error_file = os.path.join(error_dir, f"errors_{date_str}.jsonl")
            
            with open(error_file, "a", encoding="utf-8") as f:
                error_dict = asdict(error_event)
                error_dict["timestamp"] = error_event.timestamp.isoformat()
                error_dict["severity"] = error_event.severity.value
                f.write(json.dumps(error_dict) + "\n")
                
        except Exception as e:
            logger.error(f"Failed to save error to file: {e}")
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the specified time period."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_errors = [e for e in self.error_history if e.timestamp > cutoff_time]
        
        # Group by error type
        error_types = defaultdict(int)
        severity_counts = defaultdict(int)
        module_counts = defaultdict(int)
        
        for error in recent_errors:
            error_types[error.error_type] += 1
            severity_counts[error.severity.value] += 1
            module_counts[error.module] += 1
        
        return {
            "total_errors": len(recent_errors),
            "time_period_hours": hours,
            "error_types": dict(error_types),
            "severity_breakdown": dict(severity_counts),
            "module_breakdown": dict(module_counts),
            "error_rate_per_hour": len(recent_errors) / hours if hours > 0 else 0
        }
    
    def clear_old_errors(self, days: int = 7) -> None:
        """Clear errors older than specified days."""
        cutoff_time = datetime.utcnow() - timedelta(days=days)
        self.error_history = deque(
            [e for e in self.error_history if e.timestamp > cutoff_time],
            maxlen=1000
        )

# Global error monitor instance
error_monitor = ErrorMonitor()

def monitor_error(severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                  module: str = "unknown",
                  function: str = "unknown"):
    """Decorator to automatically monitor errors in functions."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_monitor.log_error(
                    error=e,
                    severity=severity,
                    module=module,
                    function=function or func.__name__
                )
                raise
        return wrapper
    return decorator
