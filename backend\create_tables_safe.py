"""
Safely create database tables without losing existing data.
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("Checking and creating database tables safely...")
    from database import engine, Base, SessionLocal
    import crud
    
    # Create tables only if they don't exist (preserves existing data)
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables checked/created successfully")
    
    # Test database connection
    print("Testing database connection...")
    db = SessionLocal()
    try:
        # Try to query existing scans to see if we have data
        from sqlalchemy import text
        result = db.execute(text("SELECT COUNT(*) FROM scans")).scalar()
        print(f"✅ Database connection successful. Found {result} existing scans.")
        
        # Initialize default parameters only if they don't exist
        print("Checking default parameters...")
        crud.initialize_default_parameters(db)
        print("✅ Default parameters checked/initialized")
        
    finally:
        db.close()
    
    print("\n🎉 Database setup completed successfully!")
    print("Existing data has been preserved.")
    print("You can now run the calculation tests.")
    
except Exception as e:
    print(f"❌ Error setting up database: {e}")
    import traceback
    traceback.print_exc()
