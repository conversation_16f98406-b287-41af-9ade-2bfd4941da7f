import React, { memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import {
  BarChart, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';

interface ChartData {
  name: string;
  value: number;
}

interface ChartCardProps {
  title: string;
  data: ChartData[];
  type: 'bar' | 'pie';
  dataKey?: string;
  colors?: string[];
}

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  data,
  type = 'bar',
  dataKey = 'value',
  colors = ['#3b82f6', '#10b981', '#f59e0b', '#ec4899', '#8b5cf6', '#ef4444', '#06b6d4', '#84cc16']
}) => {
  const { theme } = useTheme();

  const renderChart = () => {
    // Check if data is empty or invalid
    console.log('ChartCard rendering with data:', { title, data, dataLength: data?.length });

    if (!data || data.length === 0) {
      return (
        <div className={`flex flex-col items-center justify-center h-[300px] ${
          theme === 'light' ? 'text-gray-500' : 'text-gray-400'
        }`}>
          <div className={`w-16 h-16 rounded-full mb-4 flex items-center justify-center ${
            theme === 'light' ? 'bg-gray-100' : 'bg-gray-700'
          }`}>
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p className="text-lg font-semibold">No data available</p>
          <p className="text-sm">Chart will appear when performance data is calculated</p>
        </div>
      );
    }

    // Check if all values are zero
    const hasNonZeroValues = data.some(item => (item.value || 0) > 0);
    if (!hasNonZeroValues) {
      return (
        <div className={`flex flex-col items-center justify-center h-[300px] ${
          theme === 'light' ? 'text-gray-500' : 'text-gray-400'
        }`}>
          <div className={`w-16 h-16 rounded-full mb-4 flex items-center justify-center ${
            theme === 'light' ? 'bg-yellow-100' : 'bg-yellow-900'
          }`}>
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-lg font-semibold">No activity recorded</p>
          <p className="text-sm">All values are currently zero</p>
        </div>
      );
    }

    if (type === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'light' ? '#e5e7eb' : '#374151'} />
            <XAxis
              dataKey="name"
              tick={{ fill: theme === 'light' ? '#4b5563' : '#9ca3af' }}
            />
            <YAxis
              tick={{ fill: theme === 'light' ? '#4b5563' : '#9ca3af' }}
              tickFormatter={(value) => {
                if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
                return value.toString();
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: theme === 'light' ? '#ffffff' : '#1f2937',
                borderColor: theme === 'light' ? '#e5e7eb' : '#374151',
                color: theme === 'light' ? '#111827' : '#f9fafb',
              }}
              formatter={(value: number) => {
                if (value >= 1000000) return [`${(value / 1000000).toFixed(2)}M`, ''];
                if (value >= 1000) return [`${(value / 1000).toFixed(1)}K`, ''];
                return [value, ''];
              }}
            />
            <Legend wrapperStyle={{ color: theme === 'light' ? '#4b5563' : '#9ca3af' }} />
            <Bar
              dataKey={dataKey}
              fill="url(#colorGradient)"
              radius={[4, 4, 0, 0]}
            >
              <defs>
                <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#1d4ed8" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      );
    } else if (type === 'pie') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              fill="#8884d8"
              dataKey={dataKey}
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: theme === 'light' ? '#ffffff' : '#1f2937',
                borderColor: theme === 'light' ? '#e5e7eb' : '#374151',
                color: theme === 'light' ? '#111827' : '#f9fafb',
              }}
              formatter={(value: number) => {
                if (value >= 1000000) return [`${(value / 1000000).toFixed(2)}M`, ''];
                if (value >= 1000) return [`${(value / 1000).toFixed(1)}K`, ''];
                return [value, ''];
              }}
            />
            <Legend wrapperStyle={{ color: theme === 'light' ? '#4b5563' : '#9ca3af' }} />
          </PieChart>
        </ResponsiveContainer>
      );
    }

    return null;
  };

  return (
    <div className={`p-6 rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl ${
      theme === 'light'
        ? 'bg-white hover:bg-gray-50'
        : 'bg-gray-800 hover:bg-gray-750'
    }`}>
      <div className="flex items-center mb-6">
        <div className={`w-1 h-8 rounded-full mr-4 ${
          theme === 'light' ? 'bg-gradient-to-b from-blue-500 to-indigo-600' : 'bg-gradient-to-b from-blue-400 to-indigo-500'
        }`}></div>
        <h3 className={`text-xl font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
          {title}
        </h3>
      </div>
      <div className="relative">
        {renderChart()}
      </div>
    </div>
  );
};

export default memo(ChartCard);
