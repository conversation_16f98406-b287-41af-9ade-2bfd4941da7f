from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud, schemas, services, models
from database import get_db

router = APIRouter(
    prefix="/players",
    tags=["Players"],
    responses={404: {"description": "Not found"}},
)

@router.get("/", response_model=List[schemas.Player])
def read_players(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """
    Retrieve a list of all players.
    """
    players = crud.get_players(db, skip=skip, limit=limit)
    return players

@router.get("/{player_id}", response_model=schemas.Player)
def read_player(
    player_id: int, 
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific player by their ID.
    """
    db_player = crud.get_player(db, player_id=player_id)
    if db_player is None:
        raise HTTPException(status_code=404, detail="Player not found")
    return db_player

@router.get("/{player_id}/stats", response_model=List[schemas.PlayerStatWithScanInfo])
def read_player_stats_history(
    player_id: int, 
    db: Session = Depends(get_db)
):
    """
    Retrieve all historical statistics for a specific player, ordered by scan.
    """
    db_player = crud.get_player(db, player_id=player_id)
    if db_player is None:
        raise HTTPException(status_code=404, detail="Player not found")
    
    # Fetch stats and eager load player and scan details for the response model
    # This assumes PlayerStatWithScanInfo is defined in schemas.py and includes Scan info
    stats = db.query(models.PlayerStat).filter(models.PlayerStat.player_id == player_id).join(models.Scan).order_by(models.Scan.timestamp).all()
    return stats

@router.get("/{player_id}/deltas", response_model=List[schemas.DeltaStat])
def read_player_delta_stats(
    player_id: int, 
    db: Session = Depends(get_db)
):
    """
    Retrieve all calculated delta statistics for a specific player.
    """
    db_player = crud.get_player(db, player_id=player_id)
    if db_player is None:
        raise HTTPException(status_code=404, detail="Player not found")
    deltas = crud.get_delta_stats_for_player(db, player_id=player_id)
    return deltas

@router.get("/{player_id}/summary", response_model=Optional[schemas.PlayerPerformanceSummary])
def get_single_player_performance_summary(
    player_id: int,
    current_scan_id: Optional[int] = None, # If None, latest scan is used
    baseline_scan_id: Optional[int] = None, # If None, default baseline is used
    db: Session = Depends(get_db)
):
    """
    Get a performance summary for a single player.
    Uses the latest scan as current_scan_id if not provided.
    Uses the default baseline scan if baseline_scan_id is not provided.
    """
    if current_scan_id is None:
        latest_scan = crud.get_latest_scan(db)
        if not latest_scan:
            raise HTTPException(status_code=404, detail="No scans available to generate summary.")
        current_scan_id = latest_scan.id
    
    summary = services.get_player_performance_summary(
        db=db, 
        player_id=player_id, 
        current_scan_id=current_scan_id, 
        baseline_scan_id=baseline_scan_id
    )
    if summary is None:
        raise HTTPException(status_code=404, detail="Player or their stats not found for the given scan IDs.")
    return summary

@router.post("/find", response_model=Optional[schemas.Player])
def find_player_by_name_or_id(
    name: Optional[str] = None,
    governor_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Find a player by name or governor ID.
    Governor ID is prioritized if both are provided.
    """
    if not name and not governor_id:
        raise HTTPException(status_code=400, detail="Either player name or governor_id must be provided.")
    
    player = None
    if governor_id:
        player = crud.get_player_by_gov_id(db, governor_id=governor_id)
    
    if not player and name:
        player = crud.get_player_by_name(db, name=name)
        
    if player is None:
        raise HTTPException(status_code=404, detail="Player not found with the given criteria.")
    return player