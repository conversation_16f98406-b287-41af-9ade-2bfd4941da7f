"""
Advanced Security Features
Implements comprehensive security measures including threat detection, audit logging, and advanced authentication.
"""

import hashlib
import hmac
import secrets
import time
import json
import ipaddress
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import re
import logging
from fastapi import HTT<PERSON>Exception, Request, status
from sqlalchemy.orm import Session
import geoip2.database
import geoip2.errors

logger = logging.getLogger(__name__)

@dataclass
class SecurityEvent:
    """Security event data structure."""
    event_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    source_ip: str
    user_id: Optional[str]
    timestamp: datetime
    details: Dict[str, Any]
    action_taken: Optional[str] = None

@dataclass
class ThreatIntelligence:
    """Threat intelligence data."""
    ip_address: str
    threat_level: str  # 'low', 'medium', 'high', 'critical'
    threat_types: List[str]  # ['malware', 'botnet', 'scanner', 'tor']
    last_seen: datetime
    source: str

class SecurityAuditLogger:
    """Comprehensive security audit logging."""
    
    def __init__(self, max_events: int = 10000):
        self.events: deque = deque(maxlen=max_events)
        self.event_counts = defaultdict(int)
        self._lock = threading.Lock()
    
    def log_event(self, event: SecurityEvent):
        """Log a security event."""
        with self._lock:
            self.events.append(event)
            self.event_counts[event.event_type] += 1
            
            # Log to standard logger based on severity
            log_message = f"Security Event: {event.event_type} from {event.source_ip}"
            if event.user_id:
                log_message += f" (User: {event.user_id})"
            
            if event.severity == 'critical':
                logger.critical(log_message, extra=asdict(event))
            elif event.severity == 'high':
                logger.error(log_message, extra=asdict(event))
            elif event.severity == 'medium':
                logger.warning(log_message, extra=asdict(event))
            else:
                logger.info(log_message, extra=asdict(event))
    
    def get_events(self, limit: int = 100, event_type: str = None, 
                   severity: str = None, since: datetime = None) -> List[SecurityEvent]:
        """Get filtered security events."""
        with self._lock:
            events = list(self.events)
            
            # Apply filters
            if event_type:
                events = [e for e in events if e.event_type == event_type]
            if severity:
                events = [e for e in events if e.severity == severity]
            if since:
                events = [e for e in events if e.timestamp >= since]
            
            # Sort by timestamp (newest first) and limit
            events.sort(key=lambda x: x.timestamp, reverse=True)
            return events[:limit]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get security event statistics."""
        with self._lock:
            now = datetime.utcnow()
            last_24h = now - timedelta(hours=24)
            last_hour = now - timedelta(hours=1)
            
            events_24h = [e for e in self.events if e.timestamp >= last_24h]
            events_1h = [e for e in self.events if e.timestamp >= last_hour]
            
            return {
                'total_events': len(self.events),
                'events_last_24h': len(events_24h),
                'events_last_hour': len(events_1h),
                'event_types': dict(self.event_counts),
                'severity_breakdown': {
                    severity: len([e for e in self.events if e.severity == severity])
                    for severity in ['low', 'medium', 'high', 'critical']
                }
            }

class ThreatDetector:
    """Advanced threat detection system."""
    
    def __init__(self):
        self.suspicious_patterns = {
            'sql_injection': [
                r'(\bUNION\b.*\bSELECT\b)',
                r'(\bOR\b.*=.*\bOR\b)',
                r'(\'.*OR.*\'.*=.*\')',
                r'(--|\#|/\*|\*/)',
                r'(\bDROP\b.*\bTABLE\b)',
                r'(\bINSERT\b.*\bINTO\b)',
                r'(\bUPDATE\b.*\bSET\b)',
                r'(\bDELETE\b.*\bFROM\b)'
            ],
            'xss': [
                r'<script[^>]*>.*?</script>',
                r'javascript:',
                r'on\w+\s*=',
                r'<iframe[^>]*>',
                r'<object[^>]*>',
                r'<embed[^>]*>'
            ],
            'path_traversal': [
                r'\.\./',
                r'\.\.\\',
                r'%2e%2e%2f',
                r'%2e%2e%5c'
            ],
            'command_injection': [
                r'[;&|`]',
                r'\$\(',
                r'`.*`',
                r'\|\s*\w+',
                r'&&\s*\w+',
                r';\s*\w+'
            ]
        }
        
        self.known_bad_ips: Set[str] = set()
        self.suspicious_ips: Dict[str, int] = defaultdict(int)
        self.failed_attempts: Dict[str, List[datetime]] = defaultdict(list)
    
    def analyze_request(self, request: Request, user_input: Dict[str, Any] = None) -> List[str]:
        """Analyze request for threats."""
        threats = []
        
        # Analyze URL path
        path_threats = self._check_patterns(request.url.path, 'path_traversal')
        threats.extend([f"Path traversal attempt: {threat}" for threat in path_threats])
        
        # Analyze query parameters
        for param, value in request.query_params.items():
            for threat_type, patterns in self.suspicious_patterns.items():
                if self._check_patterns(str(value), threat_type):
                    threats.append(f"{threat_type.upper()} in query param '{param}': {value}")
        
        # Analyze user input if provided
        if user_input:
            for field, value in user_input.items():
                if isinstance(value, str):
                    for threat_type, patterns in self.suspicious_patterns.items():
                        if self._check_patterns(value, threat_type):
                            threats.append(f"{threat_type.upper()} in field '{field}': {value}")
        
        # Check for suspicious user agents
        user_agent = request.headers.get('user-agent', '').lower()
        suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan', 'zap', 'burp']
        if any(agent in user_agent for agent in suspicious_agents):
            threats.append(f"Suspicious user agent: {user_agent}")
        
        return threats
    
    def _check_patterns(self, text: str, threat_type: str) -> List[str]:
        """Check text against threat patterns."""
        if threat_type not in self.suspicious_patterns:
            return []
        
        matches = []
        for pattern in self.suspicious_patterns[threat_type]:
            if re.search(pattern, text, re.IGNORECASE):
                matches.append(pattern)
        
        return matches
    
    def track_failed_attempt(self, ip_address: str):
        """Track failed authentication attempts."""
        now = datetime.utcnow()
        self.failed_attempts[ip_address].append(now)
        
        # Clean old attempts (older than 1 hour)
        cutoff = now - timedelta(hours=1)
        self.failed_attempts[ip_address] = [
            attempt for attempt in self.failed_attempts[ip_address]
            if attempt > cutoff
        ]
        
        # Check for brute force
        if len(self.failed_attempts[ip_address]) >= 10:  # 10 failed attempts in 1 hour
            self.suspicious_ips[ip_address] += 1
            return True
        
        return False
    
    def is_suspicious_ip(self, ip_address: str) -> bool:
        """Check if IP is suspicious."""
        return (ip_address in self.known_bad_ips or 
                self.suspicious_ips[ip_address] >= 3)

class GeolocationService:
    """IP geolocation service for security analysis."""
    
    def __init__(self, geoip_db_path: str = None):
        self.reader = None
        if geoip_db_path:
            try:
                self.reader = geoip2.database.Reader(geoip_db_path)
            except Exception as e:
                logger.warning(f"Failed to load GeoIP database: {e}")
    
    def get_location(self, ip_address: str) -> Dict[str, Any]:
        """Get location information for IP address."""
        if not self.reader:
            return {'country': 'Unknown', 'city': 'Unknown', 'risk_score': 0}
        
        try:
            response = self.reader.city(ip_address)
            
            # Calculate risk score based on location
            risk_score = 0
            high_risk_countries = ['CN', 'RU', 'KP', 'IR']  # Example high-risk countries
            if response.country.iso_code in high_risk_countries:
                risk_score += 3
            
            # Check if it's a known hosting provider (higher risk for attacks)
            if 'hosting' in response.traits.user_type.lower() if response.traits.user_type else False:
                risk_score += 2
            
            return {
                'country': response.country.name,
                'country_code': response.country.iso_code,
                'city': response.city.name,
                'latitude': float(response.location.latitude) if response.location.latitude else None,
                'longitude': float(response.location.longitude) if response.location.longitude else None,
                'risk_score': risk_score,
                'is_anonymous_proxy': response.traits.is_anonymous_proxy,
                'is_satellite_provider': response.traits.is_satellite_provider
            }
        except geoip2.errors.AddressNotFoundError:
            return {'country': 'Unknown', 'city': 'Unknown', 'risk_score': 0}
        except Exception as e:
            logger.error(f"Geolocation lookup failed for {ip_address}: {e}")
            return {'country': 'Unknown', 'city': 'Unknown', 'risk_score': 0}

class SecurityManager:
    """Main security management system."""
    
    def __init__(self):
        self.audit_logger = SecurityAuditLogger()
        self.threat_detector = ThreatDetector()
        self.geolocation = GeolocationService()
        self.session_tokens: Dict[str, Dict[str, Any]] = {}
        self.csrf_tokens: Dict[str, datetime] = {}
    
    def analyze_request_security(self, request: Request, user_input: Dict[str, Any] = None) -> Dict[str, Any]:
        """Comprehensive request security analysis."""
        client_ip = self._get_client_ip(request)
        
        # Threat detection
        threats = self.threat_detector.analyze_request(request, user_input)
        
        # Geolocation analysis
        location = self.geolocation.get_location(client_ip)
        
        # Check if IP is suspicious
        is_suspicious = self.threat_detector.is_suspicious_ip(client_ip)
        
        # Calculate overall risk score
        risk_score = location.get('risk_score', 0)
        if threats:
            risk_score += len(threats) * 2
        if is_suspicious:
            risk_score += 5
        
        # Log security event if high risk
        if risk_score >= 5 or threats:
            severity = 'critical' if risk_score >= 10 else 'high' if risk_score >= 7 else 'medium'
            
            event = SecurityEvent(
                event_type='suspicious_request',
                severity=severity,
                source_ip=client_ip,
                user_id=None,  # Will be set by caller if available
                timestamp=datetime.utcnow(),
                details={
                    'threats': threats,
                    'location': location,
                    'risk_score': risk_score,
                    'user_agent': request.headers.get('user-agent'),
                    'path': str(request.url.path),
                    'method': request.method
                }
            )
            self.audit_logger.log_event(event)
        
        return {
            'threats': threats,
            'location': location,
            'risk_score': risk_score,
            'is_suspicious': is_suspicious,
            'action_required': risk_score >= 8
        }
    
    def generate_csrf_token(self, session_id: str) -> str:
        """Generate CSRF token for session."""
        token = secrets.token_urlsafe(32)
        self.csrf_tokens[token] = datetime.utcnow()
        return token
    
    def validate_csrf_token(self, token: str) -> bool:
        """Validate CSRF token."""
        if token not in self.csrf_tokens:
            return False
        
        # Check if token is expired (1 hour)
        if datetime.utcnow() - self.csrf_tokens[token] > timedelta(hours=1):
            del self.csrf_tokens[token]
            return False
        
        return True
    
    def create_secure_session(self, user_id: str, ip_address: str) -> str:
        """Create secure session with additional metadata."""
        session_id = secrets.token_urlsafe(32)
        
        self.session_tokens[session_id] = {
            'user_id': user_id,
            'ip_address': ip_address,
            'created_at': datetime.utcnow(),
            'last_activity': datetime.utcnow(),
            'csrf_token': self.generate_csrf_token(session_id)
        }
        
        return session_id
    
    def validate_session(self, session_id: str, ip_address: str) -> bool:
        """Validate session with IP binding."""
        if session_id not in self.session_tokens:
            return False
        
        session = self.session_tokens[session_id]
        
        # Check IP binding
        if session['ip_address'] != ip_address:
            self.audit_logger.log_event(SecurityEvent(
                event_type='session_hijack_attempt',
                severity='high',
                source_ip=ip_address,
                user_id=session['user_id'],
                timestamp=datetime.utcnow(),
                details={
                    'original_ip': session['ip_address'],
                    'attempted_ip': ip_address,
                    'session_id': session_id
                }
            ))
            return False
        
        # Check session timeout (24 hours)
        if datetime.utcnow() - session['last_activity'] > timedelta(hours=24):
            del self.session_tokens[session_id]
            return False
        
        # Update last activity
        session['last_activity'] = datetime.utcnow()
        return True
    
    def _get_client_ip(self, request: Request) -> str:
        """Get real client IP considering proxies."""
        # Check for forwarded headers
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else 'unknown'
    
    def get_security_dashboard(self) -> Dict[str, Any]:
        """Get security dashboard data."""
        return {
            'audit_stats': self.audit_logger.get_statistics(),
            'active_sessions': len(self.session_tokens),
            'suspicious_ips': len(self.threat_detector.suspicious_ips),
            'recent_threats': len(self.audit_logger.get_events(
                limit=100, 
                since=datetime.utcnow() - timedelta(hours=24)
            ))
        }

# Global security manager
security_manager = SecurityManager()

# Export main components
__all__ = [
    'security_manager',
    'SecurityEvent',
    'ThreatIntelligence',
    'SecurityAuditLogger',
    'ThreatDetector',
    'GeolocationService'
]
