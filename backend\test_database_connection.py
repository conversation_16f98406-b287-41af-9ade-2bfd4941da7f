#!/usr/bin/env python3
"""
Quick database connection test to verify the application is ready.
This script tests basic database connectivity and core functionality.
"""

import os
import sys
import sqlite3
from pathlib import Path

def test_database_file():
    """Test if database file exists and is accessible."""
    print("🔍 Testing database file...")
    
    db_files = ['kvk_tracker.db', 'data_website.db', '../kvk_tracker.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"✅ Found database: {db_file}")
            
            try:
                # Test basic connection
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Check tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"✅ Database has {len(tables)} tables")
                
                # Check if we have data
                if any('players' in str(table) for table in tables):
                    cursor.execute("SELECT COUNT(*) FROM players")
                    player_count = cursor.fetchone()[0]
                    print(f"✅ Found {player_count} players in database")
                
                if any('scans' in str(table) for table in tables):
                    cursor.execute("SELECT COUNT(*) FROM scans")
                    scan_count = cursor.fetchone()[0]
                    print(f"✅ Found {scan_count} scans in database")
                
                conn.close()
                return True
                
            except Exception as e:
                print(f"❌ Database connection error: {e}")
                return False
    
    print("❌ No database file found")
    return False

def test_python_imports():
    """Test if all required Python modules can be imported."""
    print("\n🔍 Testing Python imports...")
    
    required_modules = [
        'fastapi', 'sqlalchemy', 'pandas', 'openpyxl', 
        'uvicorn', 'python_multipart', 'passlib'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️  Missing modules: {', '.join(missing_modules)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True

def test_application_imports():
    """Test if application modules can be imported."""
    print("\n🔍 Testing application imports...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        import models
        print("✅ models.py")
        
        import database
        print("✅ database.py")
        
        import crud
        print("✅ crud.py")
        
        import services
        print("✅ services.py")
        
        import calculations
        print("✅ calculations.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Application import error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 RISE OF KINGDOMS - DATABASE CONNECTION TEST")
    print("=" * 60)
    
    tests = [
        ("Database File Access", test_database_file),
        ("Python Dependencies", test_python_imports),
        ("Application Modules", test_application_imports)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The application is ready to start.")
        print("\nNext steps:")
        print("1. Start the backend: uvicorn main:app --reload")
        print("2. Start the frontend: npm run dev")
        print("3. Test scan upload functionality")
    else:
        print("\n⚠️  Some tests failed. Please resolve the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
