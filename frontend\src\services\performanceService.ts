import {
  PlayerScanData,
  ScanData,
  PerformanceMetrics,
  PerformanceThresholds,
  AlliancePerformance,
  KingdomPerformance,
  getGradeFromScore,
  defaultThresholds
} from '../types/dataTypes';

/**
 * Calculate performance metrics between two scans
 */
export function calculatePerformanceMetrics(
  baselineScan: ScanData,
  currentScan: ScanData,
  thresholds: PerformanceThresholds = defaultThresholds
): PerformanceMetrics[] {
  const metrics: PerformanceMetrics[] = [];

  // Process each player in the current scan
  currentScan.players.forEach(currentPlayer => {
    // Find the same player in the baseline scan
    const baselinePlayer = baselineScan.players.find(p => p.governorId === currentPlayer.governorId);

    // Skip if player wasn't in the baseline scan
    if (!baselinePlayer) return;

    // Calculate growth metrics
    const powerGrowth = currentPlayer.power - baselinePlayer.power;

    const killPointsGrowth = {
      total: currentPlayer.killPoints - baselinePlayer.killPoints, // PlayerScanData.killPoints is a number
      t1: (currentPlayer.t1Kills || 0) - (baselinePlayer.t1Kills || 0),
      t2: (currentPlayer.t2Kills || 0) - (baselinePlayer.t2Kills || 0),
      t3: (currentPlayer.t3Kills || 0) - (baselinePlayer.t3Kills || 0),
      t4: (currentPlayer.t4Kills || 0) - (baselinePlayer.t4Kills || 0),
      t5: (currentPlayer.t5Kills || 0) - (baselinePlayer.t5Kills || 0),
      t45: ((currentPlayer.t4Kills || 0) + (currentPlayer.t5Kills || 0)) -
           ((baselinePlayer.t4Kills || 0) + (baselinePlayer.t5Kills || 0)) // Renamed to t45
    };

    const deadTroopsGrowth = currentPlayer.deads - baselinePlayer.deads; // PlayerScanData.deads is a number

    // PlayerScanData.rssGathered is a number
    const resourcesGatheredGrowth = (currentPlayer.rssGathered || 0) - (baselinePlayer.rssGathered || 0);

    const resourceAssistanceGrowth = (currentPlayer.rssAssisted || 0) - (baselinePlayer.rssAssisted || 0); // PlayerScanData.rssAssisted
    const helpCountGrowth = (currentPlayer.helps || 0) - (baselinePlayer.helps || 0); // PlayerScanData.helps
    const allianceHonorGrowth = (currentPlayer.allianceHonor || 0) - (baselinePlayer.allianceHonor || 0);

    // Calculate performance scores (0-100 scale)
    const powerScore = calculateScore(powerGrowth, thresholds.power.min, thresholds.power.target, thresholds.power.excellent);
    const killsScore = calculateScore(killPointsGrowth.total, thresholds.killPoints.min, thresholds.killPoints.target, thresholds.killPoints.excellent);
    const t4t5KillsScore = calculateScore(killPointsGrowth.t45, thresholds.t4t5Kills.min, thresholds.t4t5Kills.target, thresholds.t4t5Kills.excellent); // Corrected to t45
    const deadsScore = calculateScore(deadTroopsGrowth, thresholds.deadTroops.min, thresholds.deadTroops.target, thresholds.deadTroops.excellent);
    const assistanceScore = calculateScore(resourceAssistanceGrowth, thresholds.resourceAssistance.min, thresholds.resourceAssistance.target, thresholds.resourceAssistance.excellent);
    const activityScore = calculateScore(helpCountGrowth, thresholds.helpCount.min, thresholds.helpCount.target, thresholds.helpCount.excellent);

    // Calculate overall score (weighted average)
    const overallScore = (
      powerScore * 0.15 +
      killsScore * 0.25 +
      t4t5KillsScore * 0.2 +
      deadsScore * 0.2 +
      assistanceScore * 0.1 +
      activityScore * 0.1
    );

    // Determine grades
    const powerGrade = getGradeFromScore(powerScore);
    const killsGrade = getGradeFromScore(killsScore);
    const t4t5KillsGrade = getGradeFromScore(t4t5KillsScore);
    const deadsGrade = getGradeFromScore(deadsScore);
    const assistanceGrade = getGradeFromScore(assistanceScore);
    const activityGrade = getGradeFromScore(activityScore);
    const overallGrade = getGradeFromScore(overallScore);

    // Calculate KP per dead ratio
    const kpPerDead = deadTroopsGrowth > 0 ? killPointsGrowth.total / deadTroopsGrowth : 0;

    // Check for zeroed status
    let isZeroed = false;
    if (powerGrowth < 0) { // Only check for zeroed if power decreased
      const powerLoss = Math.abs(powerGrowth);
      const powerDropSignificantAbs = powerLoss >= thresholds.zeroedPowerDropAbs;
      const powerDropSignificantPct = baselinePlayer.power > 0 &&
                                     (powerLoss / baselinePlayer.power) >= thresholds.zeroedPowerDropPct;

      if ((powerDropSignificantAbs || powerDropSignificantPct) &&
          deadTroopsGrowth >= thresholds.significantDeadForZeroed) {
        isZeroed = true;
      }
    }

    // Check for underperformance
    const underperformanceReasons: string[] = [];

    if (powerGrowth < thresholds.power.min && !isZeroed) {
      underperformanceReasons.push(`Low power growth: ${formatNumber(powerGrowth)} (min: ${formatNumber(thresholds.power.min)})`);
    }

    if (killPointsGrowth.total < thresholds.killPoints.min) {
      underperformanceReasons.push(`Low kill points: ${formatNumber(killPointsGrowth.total)} (min: ${formatNumber(thresholds.killPoints.min)})`);
    }

    if (killPointsGrowth.t4t5 < thresholds.t4t5Kills.min) {
      underperformanceReasons.push(`Low T4/T5 kills: ${formatNumber(killPointsGrowth.t4t5)} (min: ${formatNumber(thresholds.t4t5Kills.min)})`);
    }

    if (deadTroopsGrowth < thresholds.deadTroops.min) {
      underperformanceReasons.push(`Low dead troops: ${formatNumber(deadTroopsGrowth)} (min: ${formatNumber(thresholds.deadTroops.min)})`);
    }

    if (resourceAssistanceGrowth < thresholds.resourceAssistance.min) {
      underperformanceReasons.push(`Low resource assistance: ${formatNumber(resourceAssistanceGrowth)} (min: ${formatNumber(thresholds.resourceAssistance.min)})`);
    }

    if (helpCountGrowth < thresholds.helpCount.min) {
      underperformanceReasons.push(`Low help count: ${helpCountGrowth} (min: ${thresholds.helpCount.min})`);
    }

    if (deadTroopsGrowth > 0 && kpPerDead < thresholds.kpPerDeadMin) {
      underperformanceReasons.push(`Low KP per dead ratio: ${kpPerDead.toFixed(1)} (min: ${thresholds.kpPerDeadMin})`);
    }

    // Zeroed players are not considered underperforming
    const isUnderperforming = !isZeroed && (underperformanceReasons.length > 0 || overallScore < 60);

    // If overall score is low but no specific reasons, add a general reason
    if (isUnderperforming && underperformanceReasons.length === 0) {
      underperformanceReasons.push(`Low overall performance score: ${overallScore.toFixed(1)}`);
    }

    // Calculate KP per dead score and grade
    const kpPerDeadScore = calculateScore(
      kpPerDead,
      thresholds.kpPerDeadMin,
      thresholds.kpPerDeadTarget,
      thresholds.kpPerDeadExcellent
    );
    const kpPerDeadGrade = getGradeFromScore(kpPerDeadScore);

    // Create the performance metrics object
    metrics.push({
      playerId: currentPlayer.governorId, // Use governorId as primary ID
      playerName: currentPlayer.name,
      alliance: currentPlayer.alliance || 'N/A', // Fallback for undefined alliance
      powerGrowth,
      killPointsGrowth,
      deadsGrowth: deadTroopsGrowth, // Ensure correct variable name
      rssGatheredGrowth: resourcesGatheredGrowth, // Corrected variable name
      rssAssistedGrowth: resourceAssistanceGrowth, // Corrected variable name
      helpCountGrowth,
      allianceHonorGrowth,
      kpPerDead,
      isZeroed,
      scores: {
        powerScore,
        killsScore,
        t4t5KillsScore,
        deadsScore,
        assistanceScore,
        activityScore,
        overallScore,
        kpPerDeadScore
      },
      grades: {
        powerGrade,
        killsGrade,
        t4t5KillsGrade,
        deadsGrade,
        assistanceGrade,
        activityGrade,
        overallGrade,
        kpPerDeadGrade
      },
      isUnderperforming,
      underperformanceReasons
    });
  });

  return metrics;
}

/**
 * Calculate alliance performance metrics
 */
export function calculateAlliancePerformance(
  playerMetrics: PerformanceMetrics[]
): AlliancePerformance[] {
  // Group players by alliance
  const allianceMap = new Map<string, PerformanceMetrics[]>();

  playerMetrics.forEach(player => {
    const allianceName = player.alliance || 'No Alliance'; // Handle undefined alliance
    if (!allianceMap.has(allianceName)) {
      allianceMap.set(allianceName, []);
    }
    allianceMap.get(allianceName)!.push(player);
  });

  // Calculate metrics for each alliance
  return Array.from(allianceMap.entries()).map(([name, players]) => {
    // Sort players by overall score
    const sortedPlayers = [...players].sort((a, b) => b.scores.overallScore - a.scores.overallScore);

    // Get top performers (top 20% or at least 3 players)
    const topCount = Math.max(3, Math.ceil(players.length * 0.2));
    const topPerformers = sortedPlayers.slice(0, topCount).map(p => p.playerId);

    // Get underperformers
    const underperformers = players.filter(p => p.isUnderperforming).map(p => p.playerId);

    return {
      name,
      memberCount: players.length,
      averagePower: average(players, p => p.powerGrowth),
      totalPower: sum(players, p => p.powerGrowth),
      averageKillPoints: average(players, p => p.killPointsGrowth.total),
      totalKillPoints: sum(players, p => p.killPointsGrowth.total),
      averageDeads: average(players, p => p.deadsGrowth), // Corrected from averageDeadTroops
      totalDeads: sum(players, p => p.deadsGrowth), // Corrected from totalDeadTroops
      averageT45Kills: average(players, p => p.killPointsGrowth.t45), // Corrected to t45
      totalT45Kills: sum(players, p => p.killPointsGrowth.t45), // Corrected to t45
      averageRssAssisted: average(players, p => p.rssAssistedGrowth), // Added rssAssisted
      totalRssAssisted: sum(players, p => p.rssAssistedGrowth), // Added rssAssisted
      averageScore: average(players, p => p.scores.overallScore),
      underperformingCount: underperformers.length,
      underperformingPercentage: (underperformers.length / players.length) * 100,
      topPerformers,
      underperformers
    };
  });
}

/**
 * Calculate kingdom performance summary
 */
export function calculateKingdomPerformance(
  playerMetrics: PerformanceMetrics[],
  alliancePerformance: AlliancePerformance[]
): KingdomPerformance {
  // Count players by grade
  const gradeDistribution = {
    S: playerMetrics.filter(p => p.grades.overallGrade === 'S').length,
    A: playerMetrics.filter(p => p.grades.overallGrade === 'A').length,
    B: playerMetrics.filter(p => p.grades.overallGrade === 'B').length,
    C: playerMetrics.filter(p => p.grades.overallGrade === 'C').length,
    D: playerMetrics.filter(p => p.grades.overallGrade === 'D').length,
    F: playerMetrics.filter(p => p.grades.overallGrade === 'F').length
  };

  return {
    totalPlayers: playerMetrics.length,
    activePlayers: playerMetrics.filter(p => !p.isUnderperforming).length,
    totalPower: sum(playerMetrics, p => p.powerGrowth),
    totalKillPoints: sum(playerMetrics, p => p.killPointsGrowth.total),
    totalDeads: sum(playerMetrics, p => p.deadsGrowth), // Corrected from totalDeadTroops
    totalT45Kills: sum(playerMetrics, p => p.killPointsGrowth.t45), // Corrected to t45
    averageScore: average(playerMetrics, p => p.scores.overallScore),
    gradeDistribution,
    alliancePerformance
  };
}

// Helper function to calculate score based on min, target, and excellent thresholds
function calculateScore(value: number, min: number, target: number, excellent: number): number {
  if (value <= 0) return 0;

  if (value < min) {
    // Below minimum: 0-49 range
    return (value / min) * 50;
  } else if (value < target) {
    // Between minimum and target: 50-74 range
    return 50 + ((value - min) / (target - min)) * 25;
  } else if (value < excellent) {
    // Between target and excellent: 75-89 range
    return 75 + ((value - target) / (excellent - target)) * 15;
  } else {
    // Above excellent: 90-100 range
    const extraScore = Math.min(10, ((value - excellent) / excellent) * 10);
    return 90 + extraScore;
  }
}

// Helper function to calculate average
function average<T>(items: T[], selector: (item: T) => number): number {
  if (items.length === 0) return 0;
  return sum(items, selector) / items.length;
}

// Helper function to calculate sum
function sum<T>(items: T[], selector: (item: T) => number): number {
  return items.reduce((total, item) => total + selector(item), 0);
}

// Helper function to format numbers
export function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(1)}B`;
  }
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}
