# Database Configuration
DATABASE_URL=sqlite:///./data_website.db
# For PostgreSQL: DATABASE_URL=postgresql://username:password@localhost/database_name

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://yourdomain.com

# Rate Limiting Configuration
MAX_REQUESTS_PER_MINUTE=60
MAX_LOGIN_ATTEMPTS_PER_HOUR=5
MAX_UPLOAD_SIZE_MB=100

# Cache Configuration
CACHE_DEFAULT_TTL=300
CACHE_SCAN_DATA_TTL=600
CACHE_PLAYER_DATA_TTL=300
CACHE_KVK_DATA_TTL=900
CACHE_REPORTS_TTL=180

# Database Pool Configuration
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_EXTENSIONS=.xlsx,.xls,.csv

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>

# External API Configuration
EXTERNAL_API_TIMEOUT=30
EXTERNAL_API_RETRIES=3

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=backups/
