"""
Advanced Caching Strategy
Implements multi-level caching with intelligent invalidation and performance optimization.
"""

import time
import json
import hashlib
import pickle
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict
import threading
import logging
from functools import wraps

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0
    tags: List[str] = None

class IntelligentCache:
    """Multi-level cache with intelligent eviction and invalidation."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._access_times: Dict[str, float] = {}
        self._size_tracker = 0
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'invalidations': 0
        }
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate a consistent cache key."""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of cached value."""
        try:
            return len(pickle.dumps(value))
        except Exception:
            return len(str(value).encode('utf-8'))
    
    def _evict_lru(self):
        """Evict least recently used entries."""
        if not self._cache:
            return
        
        # Sort by last access time (oldest first)
        sorted_entries = sorted(
            self._cache.items(),
            key=lambda x: x[1].last_accessed or x[1].created_at
        )
        
        # Remove oldest 25% of entries
        evict_count = max(1, len(sorted_entries) // 4)
        for i in range(evict_count):
            key, entry = sorted_entries[i]
            self._remove_entry(key)
            self._stats['evictions'] += 1
            logger.debug(f"Evicted cache entry: {key}")
    
    def _remove_entry(self, key: str):
        """Remove entry and update size tracker."""
        if key in self._cache:
            entry = self._cache[key]
            self._size_tracker -= entry.size_bytes
            del self._cache[key]
            if key in self._access_times:
                del self._access_times[key]
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """Check if cache entry is expired."""
        if entry.expires_at is None:
            return False
        return datetime.utcnow() > entry.expires_at
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # Check expiration
            if self._is_expired(entry):
                self._remove_entry(key)
                self._stats['misses'] += 1
                return None
            
            # Update access statistics
            entry.access_count += 1
            entry.last_accessed = datetime.utcnow()
            self._access_times[key] = time.time()
            self._stats['hits'] += 1
            
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None):
        """Set value in cache."""
        with self._lock:
            # Calculate size and TTL
            size_bytes = self._calculate_size(value)
            ttl = ttl or self.default_ttl
            expires_at = datetime.utcnow() + timedelta(seconds=ttl) if ttl > 0 else None
            
            # Remove existing entry if present
            if key in self._cache:
                self._remove_entry(key)
            
            # Check if we need to evict entries
            while len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # Create new entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                expires_at=expires_at,
                size_bytes=size_bytes,
                tags=tags or []
            )
            
            self._cache[key] = entry
            self._access_times[key] = time.time()
            self._size_tracker += size_bytes
            
            logger.debug(f"Cached entry: {key} (size: {size_bytes} bytes, TTL: {ttl}s)")
    
    def invalidate(self, key: str):
        """Invalidate specific cache entry."""
        with self._lock:
            if key in self._cache:
                self._remove_entry(key)
                self._stats['invalidations'] += 1
                logger.debug(f"Invalidated cache entry: {key}")
    
    def invalidate_by_tags(self, tags: List[str]):
        """Invalidate all entries with specified tags."""
        with self._lock:
            keys_to_remove = []
            for key, entry in self._cache.items():
                if entry.tags and any(tag in entry.tags for tag in tags):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self._remove_entry(key)
                self._stats['invalidations'] += 1
            
            logger.debug(f"Invalidated {len(keys_to_remove)} entries by tags: {tags}")
    
    def clear(self):
        """Clear all cache entries."""
        with self._lock:
            count = len(self._cache)
            self._cache.clear()
            self._access_times.clear()
            self._size_tracker = 0
            self._stats['invalidations'] += count
            logger.info(f"Cleared all cache entries ({count} entries)")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'entries': len(self._cache),
                'max_size': self.max_size,
                'size_bytes': self._size_tracker,
                'hit_rate_percent': round(hit_rate, 2),
                'stats': self._stats.copy(),
                'memory_usage_mb': round(self._size_tracker / (1024 * 1024), 2)
            }

class CacheManager:
    """Manages multiple cache instances with different strategies."""
    
    def __init__(self):
        self.caches = {
            'dashboard': IntelligentCache(max_size=100, default_ttl=300),      # 5 minutes
            'performance': IntelligentCache(max_size=200, default_ttl=600),    # 10 minutes
            'kvk': IntelligentCache(max_size=50, default_ttl=1800),           # 30 minutes
            'scans': IntelligentCache(max_size=500, default_ttl=3600),        # 1 hour
            'players': IntelligentCache(max_size=1000, default_ttl=1800),     # 30 minutes
            'calculations': IntelligentCache(max_size=300, default_ttl=900),   # 15 minutes
        }
    
    def get_cache(self, cache_name: str) -> IntelligentCache:
        """Get specific cache instance."""
        return self.caches.get(cache_name, self.caches['dashboard'])
    
    def invalidate_related(self, operation: str):
        """Invalidate caches related to specific operations."""
        invalidation_map = {
            'scan_upload': ['dashboard', 'performance', 'calculations'],
            'kvk_update': ['kvk', 'dashboard'],
            'player_update': ['players', 'dashboard', 'performance'],
            'data_refresh': ['dashboard', 'performance', 'calculations']
        }
        
        cache_names = invalidation_map.get(operation, [])
        for cache_name in cache_names:
            if cache_name in self.caches:
                self.caches[cache_name].clear()
                logger.info(f"Invalidated {cache_name} cache due to {operation}")
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all caches."""
        return {
            cache_name: cache.get_stats()
            for cache_name, cache in self.caches.items()
        }

# Global cache manager
cache_manager = CacheManager()

def cached(cache_name: str = 'dashboard', ttl: Optional[int] = None, 
          tags: List[str] = None, key_prefix: str = None):
    """Decorator for caching function results."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache = cache_manager.get_cache(cache_name)
            
            # Generate cache key
            prefix = key_prefix or func.__name__
            cache_key = cache._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Cache the result
            cache.set(cache_key, result, ttl=ttl, tags=tags)
            
            logger.debug(f"Cached result for {func.__name__} (execution: {execution_time:.3f}s)")
            return result
        
        return wrapper
    return decorator

def cache_key_for_scan_data(scan_id: int, data_type: str) -> str:
    """Generate cache key for scan-related data."""
    return f"scan:{scan_id}:{data_type}"

def cache_key_for_kvk_data(kvk_id: int, data_type: str) -> str:
    """Generate cache key for KvK-related data."""
    return f"kvk:{kvk_id}:{data_type}"

def cache_key_for_performance_data(timeframe: str, limit: int) -> str:
    """Generate cache key for performance data."""
    return f"performance:{timeframe}:{limit}"

# Specialized caching functions
def cache_dashboard_data(data: Any, ttl: int = 300):
    """Cache dashboard data with specific TTL."""
    cache = cache_manager.get_cache('dashboard')
    cache.set('dashboard_summary', data, ttl=ttl, tags=['dashboard', 'summary'])

def cache_performance_data(data: Any, timeframe: str, ttl: int = 600):
    """Cache performance data with timeframe-specific key."""
    cache = cache_manager.get_cache('performance')
    key = cache_key_for_performance_data(timeframe, len(data.get('performance_data', [])))
    cache.set(key, data, ttl=ttl, tags=['performance', timeframe])

def cache_kvk_data(kvk_id: int, data: Any, data_type: str, ttl: int = 1800):
    """Cache KvK-specific data."""
    cache = cache_manager.get_cache('kvk')
    key = cache_key_for_kvk_data(kvk_id, data_type)
    cache.set(key, data, ttl=ttl, tags=['kvk', f'kvk_{kvk_id}', data_type])

def invalidate_scan_related_cache(scan_id: int):
    """Invalidate all cache entries related to a specific scan."""
    cache_manager.invalidate_related('scan_upload')
    
    # Also invalidate specific scan-related entries
    for cache_name in ['dashboard', 'performance', 'calculations']:
        cache = cache_manager.get_cache(cache_name)
        cache.invalidate_by_tags([f'scan_{scan_id}'])

def get_cache_statistics() -> Dict[str, Any]:
    """Get comprehensive cache statistics."""
    stats = cache_manager.get_all_stats()
    
    # Calculate overall statistics
    total_entries = sum(cache_stats['entries'] for cache_stats in stats.values())
    total_size_mb = sum(cache_stats['memory_usage_mb'] for cache_stats in stats.values())
    
    overall_hits = sum(cache_stats['stats']['hits'] for cache_stats in stats.values())
    overall_misses = sum(cache_stats['stats']['misses'] for cache_stats in stats.values())
    overall_requests = overall_hits + overall_misses
    overall_hit_rate = (overall_hits / overall_requests * 100) if overall_requests > 0 else 0
    
    return {
        'caches': stats,
        'overall': {
            'total_entries': total_entries,
            'total_size_mb': round(total_size_mb, 2),
            'overall_hit_rate_percent': round(overall_hit_rate, 2),
            'total_hits': overall_hits,
            'total_misses': overall_misses
        }
    }

# Export main components
__all__ = [
    'cache_manager',
    'cached',
    'cache_dashboard_data',
    'cache_performance_data', 
    'cache_kvk_data',
    'invalidate_scan_related_cache',
    'get_cache_statistics'
]
