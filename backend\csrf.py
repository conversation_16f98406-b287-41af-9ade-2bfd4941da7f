import secrets
from fastapi import Request, HTTPException, status, Depends

# For CSRF protection, the cookie name and header name should be consistent.
CSRF_COOKIE_NAME = "csrf_token"
CSRF_HEADER_NAME = "X-CSRF-Token"

def generate_csrf_token() -> str:
    """Generates a secure, random CSRF token string."""
    return secrets.token_urlsafe(32)

async def verify_csrf_token(request: Request):
    """
    FastAPI dependency to verify CSRF token using Double Submit Cookie pattern.
    - Retrieves token from HTTP header (e.g., X-CSRF-Token).
    - Retrieves token from cookie (e.g., csrf_token).
    - Compares them. Raises HTTPException if missing or mismatch.
    """
    csrf_token_from_header = request.headers.get(CSRF_HEADER_NAME)
    csrf_token_from_cookie = request.cookies.get(CSRF_COOKIE_NAME)

    if not csrf_token_from_header or not csrf_token_from_cookie:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="CSRF token missing from header or cookie."
        )

    if not secrets.compare_digest(csrf_token_from_header, csrf_token_from_cookie):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="CSRF token mismatch."
        )
    
    # If tokens match, the request is considered valid from a CSRF perspective.
    # No explicit return value is needed for a successful dependency check.

# Convenience dependency to apply CSRF protection
CSRFProtect = Depends(verify_csrf_token)
