#!/usr/bin/env python3
"""
Database migration script to update DeltaStat columns from Integer to BigInteger.
This fixes potential overflow issues with large power and kill point values.
"""

import sqlite3
import os
import sys
from datetime import datetime

def backup_database(db_path: str) -> str:
    """Create a backup of the database before migration."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    print(f"Creating backup: {backup_path}")
    
    # Copy the database file
    import shutil
    shutil.copy2(db_path, backup_path)
    
    return backup_path

def migrate_delta_stats_schema(db_path: str):
    """Migrate DeltaStat table to use BigInteger columns."""
    
    if not os.path.exists(db_path):
        print(f"Database file not found: {db_path}")
        return False
    
    # Create backup first
    backup_path = backup_database(db_path)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting DeltaStat schema migration...")
        
        # Check if the table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='delta_stats'
        """)
        
        if not cursor.fetchone():
            print("delta_stats table not found. Creating new table with correct schema.")
            # Table doesn't exist, create it with the correct schema
            cursor.execute("""
                CREATE TABLE delta_stats (
                    id INTEGER PRIMARY KEY,
                    player_id INTEGER NOT NULL,
                    start_scan_id INTEGER NOT NULL,
                    end_scan_id INTEGER NOT NULL,
                    power_delta BIGINT NOT NULL DEFAULT 0,
                    kill_points_delta BIGINT NOT NULL DEFAULT 0,
                    dead_troops_delta BIGINT NOT NULL DEFAULT 0,
                    kp_per_dead REAL DEFAULT 0.0,
                    is_zeroed BOOLEAN NOT NULL DEFAULT 0,
                    meets_kp_target BOOLEAN NOT NULL DEFAULT 0,
                    meets_dead_target BOOLEAN NOT NULL DEFAULT 0,
                    is_new_player BOOLEAN NOT NULL DEFAULT 0,
                    player_left_kingdom BOOLEAN NOT NULL DEFAULT 0,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (player_id) REFERENCES players (id) ON DELETE CASCADE,
                    FOREIGN KEY (start_scan_id) REFERENCES scans (id) ON DELETE CASCADE,
                    FOREIGN KEY (end_scan_id) REFERENCES scans (id) ON DELETE CASCADE
                )
            """)
            print("Created delta_stats table with BigInteger columns.")
        else:
            print("delta_stats table exists. Checking schema...")
            
            # Get current schema
            cursor.execute("PRAGMA table_info(delta_stats)")
            columns = cursor.fetchall()
            
            # Check if migration is needed
            needs_migration = False
            for col in columns:
                col_name, col_type = col[1], col[2]
                if col_name in ['power_delta', 'kill_points_delta', 'dead_troops_delta']:
                    if col_type.upper() != 'BIGINT':
                        needs_migration = True
                        print(f"Column {col_name} is {col_type}, needs migration to BIGINT")
            
            if needs_migration:
                print("Migration needed. Creating new table and copying data...")
                
                # Create new table with correct schema
                cursor.execute("""
                    CREATE TABLE delta_stats_new (
                        id INTEGER PRIMARY KEY,
                        player_id INTEGER NOT NULL,
                        start_scan_id INTEGER NOT NULL,
                        end_scan_id INTEGER NOT NULL,
                        power_delta BIGINT NOT NULL DEFAULT 0,
                        kill_points_delta BIGINT NOT NULL DEFAULT 0,
                        dead_troops_delta BIGINT NOT NULL DEFAULT 0,
                        kp_per_dead REAL DEFAULT 0.0,
                        is_zeroed BOOLEAN NOT NULL DEFAULT 0,
                        meets_kp_target BOOLEAN NOT NULL DEFAULT 0,
                        meets_dead_target BOOLEAN NOT NULL DEFAULT 0,
                        is_new_player BOOLEAN NOT NULL DEFAULT 0,
                        player_left_kingdom BOOLEAN NOT NULL DEFAULT 0,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (player_id) REFERENCES players (id) ON DELETE CASCADE,
                        FOREIGN KEY (start_scan_id) REFERENCES scans (id) ON DELETE CASCADE,
                        FOREIGN KEY (end_scan_id) REFERENCES scans (id) ON DELETE CASCADE
                    )
                """)
                
                # Copy data from old table to new table
                cursor.execute("""
                    INSERT INTO delta_stats_new 
                    SELECT * FROM delta_stats
                """)
                
                # Drop old table and rename new table
                cursor.execute("DROP TABLE delta_stats")
                cursor.execute("ALTER TABLE delta_stats_new RENAME TO delta_stats")
                
                print("Schema migration completed successfully.")
            else:
                print("Schema is already up to date.")
        
        # Create indexes for performance
        print("Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_delta_stat_scans ON delta_stats(start_scan_id, end_scan_id)",
            "CREATE INDEX IF NOT EXISTS idx_delta_stat_player_performance ON delta_stats(player_id, kp_per_dead)",
            "CREATE INDEX IF NOT EXISTS idx_delta_stat_player ON delta_stats(player_id)",
            "CREATE INDEX IF NOT EXISTS idx_delta_stat_end_scan ON delta_stats(end_scan_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        print("Migration completed successfully!")
        print(f"Backup created at: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"Migration failed: {str(e)}")
        print(f"Database backup is available at: {backup_path}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main migration function."""
    # Determine database path
    db_paths = [
        "kvk_tracker.db",
        "../kvk_tracker.db",
        "kingdom_tracker.db",
        "../kingdom_tracker.db"
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("Database file not found. Checked paths:")
        for path in db_paths:
            print(f"  - {path}")
        return False
    
    print(f"Found database: {db_path}")
    
    success = migrate_delta_stats_schema(db_path)
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("The DeltaStat table now uses BigInteger columns for better data handling.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above and try again.")
    
    return success

if __name__ == "__main__":
    main()
