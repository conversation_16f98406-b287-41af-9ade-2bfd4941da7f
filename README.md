# KvK Dashboard - Real Data Integration

A comprehensive Kingdom vs Kingdom (KvK) performance tracking dashboard that processes real Excel scan data and calculates player gains while handling name changes through player IDs.

## Features

### 🚀 Enhanced Real Data Processing
- **Excel File Support**: Process real scan data from `.xlsx` and `.csv` files
- **Smart Column Mapping**: Automatically maps various Excel column formats to standardized fields
- **Robust Data Validation**: Handles missing data, invalid entries, and different file structures
- **Multi-Sheet Support**: Processes data from sheets named 'Sheet1', 'Data', 'KvK Data', 'Player Data', or 'Scan'

### 👥 Advanced Player Tracking
- **Governor ID Priority**: Uses player IDs as primary identifier to handle name changes
- **Name Change Detection**: Automatically updates player names when they change during KvK
- **Alliance Tracking**: Monitors alliance changes and updates player records
- **New Player Detection**: Identifies and properly handles new players joining mid-KvK

### 📊 Comprehensive KvK Analytics
- **Real-time Gain Calculations**: Calculates power, kill points, and dead troop deltas
- **Performance Metrics**: 
  - Kill Points per Dead ratio
  - Efficiency scores
  - Top performer identification
  - Players needing improvement
- **Summary Statistics**: Total gains, averages, and performance distributions
- **Historical Tracking**: Compare performance across multiple scans

### 🎯 Enhanced Dashboard Features
- **Performance Summary Cards**: Visual overview of key KvK metrics
- **Real-time Data Updates**: Live performance tracking with API integration
- **Advanced Filtering**: Sort by various performance metrics
- **Responsive Design**: Works on desktop and mobile devices

## Technical Implementation

### Backend Enhancements

#### Excel Data Processing (`services.py`)
```python
# Enhanced Excel parsing with robust error handling
def parse_file_data(file_path: str, file_type: str) -> List[Dict[str, Any]]:
    # Supports multiple Excel formats and CSV files
    # Handles various column naming conventions
    # Validates essential data fields
    # Skips invalid rows gracefully
```

#### Player Management
```python
# Prioritizes governor_id for reliable player tracking
def get_or_create_player(db: Session, name: str, governor_id: Optional[str], alliance: Optional[str]):
    # 1. Match by governor_id (handles name changes)
    # 2. Fallback to name matching
    # 3. Update player information as needed
```

#### KvK Performance Analysis
```python
# Comprehensive performance summary with enhanced metrics
def get_kvk_performance_summary(db: Session, kvk_id: int, limit: int = 50):
    # Calculates delta stats between baseline and latest scans
    # Provides efficiency scores and performance indicators
    # Handles new players and missing baseline data
```

### Frontend Integration

#### Real Data API Integration
- **Performance Summary API**: `/api/reports/kvk/performance_summary`
- **Enhanced Player Data**: Includes efficiency scores, performance flags
- **Live Updates**: Real-time data fetching with React Query

#### UI Enhancements
- **4-Card Summary Layout**: KP Gain, T5 Casualties, Dead Gain, Avg KP/Dead
- **Performance Indicators**: Visual flags for top performers and improvement needs
- **Detailed Scan Information**: Analysis period, scan IDs, player counts

## Data Structure Support

### Excel Column Mapping
The system automatically maps various column formats:

| Standard Field | Supported Column Names |
|----------------|------------------------|
| Player Name | `name`, `player_name`, `governor_name`, `player` |
| Governor ID | `governor_id`, `gov_id`, `id`, `player_id` |
| Alliance | `alliance`, `alliance_tag`, `ally` |
| Power | `power`, `total_power` |
| Kill Points | `total_kill_points`, `kill_points`, `kp`, `total_kp` |
| Dead Troops | `dead_troops`, `deads`, `dead`, `total_dead` |
| T4 Kills | `t4_kills`, `t4_kill`, `tier_4_kills` |
| T5 Kills | `t5_kills`, `t5_kill`, `tier_5_kills` |

### Required Dependencies

#### Backend
```txt
openpyxl==3.1.2  # Excel file processing
xlrd==2.0.1      # Legacy Excel support
fastapi>=0.68.0
sqlalchemy>=1.4.0
uvicorn>=0.15.0
```

#### Frontend
```json
{
  "@tanstack/react-query": "^4.0.0",
  "react": "^18.0.0",
  "react-router-dom": "^6.0.0",
  "axios": "^1.0.0"
}
```

## Usage

### 1. Upload Excel Scan Data
1. Navigate to the Upload Scan page
2. Select your KvK Excel file (.xlsx or .csv)
3. Choose the appropriate KvK
4. Set as baseline scan if it's the first scan
5. Upload and process

### 2. View KvK Performance
1. Go to the KvK Detail page
2. View real-time performance summary
3. Analyze player gains and efficiency metrics
4. Sort by different performance criteria

### 3. Track Player Changes
- System automatically handles name changes using Governor IDs
- Alliance changes are tracked and updated
- New players are properly identified and integrated

## API Endpoints

### KvK Performance
```
GET /api/reports/kvk/performance_summary?kvk_id={id}&limit={limit}
```
Returns comprehensive KvK performance analysis with:
- Player performance data with efficiency metrics
- Summary statistics (total gains, averages)
- Performance indicators (top performers, needs improvement)
- Scan period information

### Scan Management
```
POST /api/scans/upload
GET /api/scans/{scan_id}/stats
GET /api/scans/
```

### Player Data
```
GET /api/players/
GET /api/players/{player_id}
```

## Performance Features

### Efficiency Metrics
- **KP per Dead Ratio**: Measures combat efficiency
- **Efficiency Score**: KP gained relative to power changes
- **Performance Flags**: Automatic identification of top performers and improvement needs

### Data Integrity
- **Duplicate Detection**: Prevents duplicate player entries
- **Data Validation**: Ensures numeric fields are properly formatted
- **Error Recovery**: Graceful handling of malformed data

## Development

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with real Excel data
5. Submit a pull request

## License

MIT License - see LICENSE file for details