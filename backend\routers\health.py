from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any
import time
import psutil
import os
from datetime import datetime

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
from logging_config import get_logger
from cache import get_cache_info, invalidate_all_cache
import crud

router = APIRouter(
    prefix="/health",
    tags=["Health"],
    responses={404: {"description": "Not found"}},
)

logger = get_logger("health")

@router.get("/", response_model=Dict[str, Any])
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Data Website API",
        "version": "0.1.0"
    }

@router.get("/detailed", response_model=Dict[str, Any])
async def detailed_health_check(db: Session = Depends(get_db)):
    """Detailed health check with database and system metrics."""
    start_time = time.time()

    health_data = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Data Website API",
        "version": "0.1.0",
        "checks": {}
    }

    # Database health check
    try:
        db.execute(text("SELECT 1"))
        db_response_time = time.time() - start_time
        health_data["checks"]["database"] = {
            "status": "healthy",
            "response_time_ms": round(db_response_time * 1000, 2)
        }
        logger.info(f"Database health check passed in {db_response_time:.3f}s")
    except Exception as e:
        health_data["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_data["status"] = "unhealthy"
        logger.error(f"Database health check failed: {e}")

    # System metrics
    try:
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        health_data["checks"]["system"] = {
            "status": "healthy",
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "percent_used": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "percent_used": round((disk.used / disk.total) * 100, 2)
            },
            "cpu_percent": psutil.cpu_percent(interval=1)
        }

        # Check if system resources are critically low
        if memory.percent > 90 or disk.percent > 90:
            health_data["checks"]["system"]["status"] = "warning"
            health_data["status"] = "degraded"

    except Exception as e:
        health_data["checks"]["system"] = {
            "status": "error",
            "error": str(e)
        }
        logger.warning(f"System metrics collection failed: {e}")

    # Application-specific checks
    try:
        # Check if we can access basic data
        player_count = len(crud.get_players(db, limit=1))
        scan_count = len(crud.get_scans(db, limit=1))

        health_data["checks"]["application"] = {
            "status": "healthy",
            "data_accessible": True,
            "has_players": player_count > 0,
            "has_scans": scan_count > 0
        }

    except Exception as e:
        health_data["checks"]["application"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_data["status"] = "unhealthy"
        logger.error(f"Application health check failed: {e}")

    # Overall response time
    total_response_time = time.time() - start_time
    health_data["response_time_ms"] = round(total_response_time * 1000, 2)

    # Log health check result
    logger.info(f"Health check completed: {health_data['status']} in {total_response_time:.3f}s")

    # Return appropriate HTTP status
    if health_data["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_data
        )
    elif health_data["status"] == "degraded":
        # Still return 200 but with warning status
        pass

    return health_data

@router.get("/ready", response_model=Dict[str, Any])
async def readiness_check(db: Session = Depends(get_db)):
    """Kubernetes-style readiness probe."""
    try:
        # Check database connectivity
        db.execute(text("SELECT 1"))

        # Check if essential data exists
        try:
            crud.get_parameters(db, limit=1)
        except Exception:
            # If parameters don't exist, try to initialize them
            crud.initialize_default_parameters(db)

        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "not_ready",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@router.get("/live", response_model=Dict[str, Any])
async def liveness_check():
    """Kubernetes-style liveness probe."""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": time.time() - psutil.boot_time()
    }

@router.get("/cache", response_model=Dict[str, Any])
async def cache_status():
    """Get cache status and statistics."""
    try:
        cache_info = get_cache_info()
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "cache": cache_info
        }
    except Exception as e:
        logger.error(f"Cache status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@router.post("/cache/clear", response_model=Dict[str, Any])
async def clear_cache():
    """Clear all cache entries (admin only)."""
    try:
        invalidate_all_cache()
        logger.info("Cache cleared via API")
        return {
            "status": "success",
            "message": "All cache entries cleared",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Cache clear failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
