#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create an admin user for the KvK Tracker application.
This script should be run once to create the initial admin user.
"""

import sys
import os
from getpass import getpass

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import SessionLocal, engine, Base
from models import User
import auth
import crud
import schemas

def create_admin_user():
    """Create an admin user interactively."""
    print("=== KvK Tracker Admin User Creation ===")
    print("This script will create an admin user for the KvK Tracker application.\n")
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    # Get database session
    db = SessionLocal()
    
    try:
        # Get user input
        username = input("Enter admin username: ").strip()
        if not username:
            print("Username cannot be empty!")
            return False
            
        # Check if user already exists
        existing_user = crud.get_user_by_username(db, username)
        if existing_user:
            print(f"User '{username}' already exists!")
            return False
            
        email = input("Enter admin email (optional): ").strip()
        if email:
            existing_email = crud.get_user_by_email(db, email)
            if existing_email:
                print(f"Email '{email}' is already registered!")
                return False
        
        # Get password securely
        password = getpass("Enter admin password: ")
        if len(password) < 8:
            print("Password must be at least 8 characters long!")
            return False
            
        password_confirm = getpass("Confirm admin password: ")
        if password != password_confirm:
            print("Passwords do not match!")
            return False
        
        # Hash the password
        hashed_password = auth.get_password_hash(password)
        
        # Create the admin user directly in the database
        admin_user = User(
            username=username,
            email=email if email else None,
            hashed_password=hashed_password,
            is_active=True,
            is_admin=True  # Set as admin
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"\n✅ Admin user '{username}' created successfully!")
        print(f"User ID: {admin_user.id}")
        print(f"Email: {admin_user.email or 'Not provided'}")
        print(f"Admin: {admin_user.is_admin}")
        print(f"Active: {admin_user.is_active}")
        print(f"Created: {admin_user.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

def list_users():
    """List all existing users."""
    db = SessionLocal()
    
    try:
        users = crud.get_users(db, limit=100)
        
        if not users:
            print("No users found in the database.")
            return
            
        print("\n=== Existing Users ===")
        print(f"{'ID':<5} {'Username':<20} {'Email':<30} {'Admin':<8} {'Active':<8}")
        print("-" * 75)
        
        for user in users:
            print(f"{user.id:<5} {user.username:<20} {user.email or 'N/A':<30} {user.is_admin:<8} {user.is_active:<8}")
            
    except Exception as e:
        print(f"❌ Error listing users: {e}")
        
    finally:
        db.close()

def main():
    """Main function to handle user interaction."""
    while True:
        print("\n=== KvK Tracker User Management ===")
        print("1. Create admin user")
        print("2. List existing users")
        print("3. Exit")
        
        choice = input("\nSelect an option (1-3): ").strip()
        
        if choice == "1":
            create_admin_user()
        elif choice == "2":
            list_users()
        elif choice == "3":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please select 1, 2, or 3.")

if __name__ == "__main__":
    main()