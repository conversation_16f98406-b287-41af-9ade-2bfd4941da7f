import React from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useTheme } from '../contexts/ThemeContext';
import { fetchScanById } from '../services/scanService';
import { formatNumber } from '../services/performanceService';
import { ScanData } from '../types/dataTypes';
import {
  FaFileAlt,
  FaCalendarAlt,
  FaUsers,
  FaShieldAlt,
  FaCrosshairs,
  FaSkullCrossbones,
  FaArrowLeft,
  FaEye,
  FaTrophy,
  FaCrown
} from 'react-icons/fa';

const ScanDetailPage: React.FC = () => {
  const { theme } = useTheme(); // Keep useTheme for now, might be used elsewhere or for explicit toggles
  const { scanId } = useParams<{ scanId: string }>();

  const {
    data: scanDetails,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['scanDetails', scanId],
    queryFn: () => fetchScanById(scanId || ''),
    enabled: !!scanId, // Only run query if scanId is available
  });

  if (isLoading) return (
    <div className="text-center py-10 text-gray-700 dark:text-gray-300">
      <div className="animate-pulse">Loading scan data...</div>
    </div>
  );

  if (error) return (
    <div className="text-center py-10 bg-red-50 text-red-600 rounded-lg p-4 max-w-2xl mx-auto border border-red-200">
      <p className="font-semibold">Error fetching scan details:</p>
      <p>{(error as Error).message}</p>
    </div>
  );

  if (!scanDetails) return (
    <div className="text-center py-10 text-gray-700 dark:text-gray-300">
      Scan not found.
    </div>
  );

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            {/* Back Button */}
            <Link
              to="/scans"
              className={`inline-flex items-center mb-6 px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                theme === 'light'
                  ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-100'
                  : 'text-blue-400 hover:text-blue-300 hover:bg-blue-900/30'
              }`}
            >
              <FaArrowLeft className="mr-2" />
              Back to Scans List
            </Link>

            <div className="flex items-center mb-6">
              <div className={`p-4 rounded-xl mr-6 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaFileAlt className="text-3xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  {scanDetails?.name || 'Scan Details'}
                </h1>
                <p className={`text-xl mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Kingdom 2358 Player Statistics Analysis
                </p>
              </div>
            </div>

            {/* Enhanced Scan Info Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/60 backdrop-blur-sm' : 'bg-gray-800/60 backdrop-blur-sm'
              }`}>
                <div className="flex items-center mb-2">
                  <FaCalendarAlt className={`text-lg mr-3 ${
                    theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                  }`} />
                  <span className={`text-sm font-medium ${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  }`}>
                    Scan Date
                  </span>
                </div>
                <p className={`text-lg font-semibold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  {scanDetails?.date ? new Date(scanDetails.date).toLocaleDateString() : 'N/A'}
                </p>
              </div>

              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/60 backdrop-blur-sm' : 'bg-gray-800/60 backdrop-blur-sm'
              }`}>
                <div className="flex items-center mb-2">
                  <FaShieldAlt className={`text-lg mr-3 ${
                    theme === 'light' ? 'text-purple-600' : 'text-purple-400'
                  }`} />
                  <span className={`text-sm font-medium ${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  }`}>
                    Event Type
                  </span>
                </div>
                <p className={`text-lg font-semibold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  {scanDetails?.kvkPhase || 'General Scan'}
                </p>
              </div>

              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/60 backdrop-blur-sm' : 'bg-gray-800/60 backdrop-blur-sm'
              }`}>
                <div className="flex items-center mb-2">
                  <FaUsers className={`text-lg mr-3 ${
                    theme === 'light' ? 'text-green-600' : 'text-green-400'
                  }`} />
                  <span className={`text-sm font-medium ${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  }`}>
                    Total Players
                  </span>
                </div>
                <p className={`text-lg font-semibold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  {scanDetails?.players?.length || 0}
                </p>
              </div>

              <div className={`p-4 rounded-xl ${
                theme === 'light' ? 'bg-white/60 backdrop-blur-sm' : 'bg-gray-800/60 backdrop-blur-sm'
              }`}>
                <div className="flex items-center mb-2">
                  <FaTrophy className={`text-lg mr-3 ${
                    scanDetails?.isBaseline
                      ? (theme === 'light' ? 'text-yellow-600' : 'text-yellow-400')
                      : (theme === 'light' ? 'text-gray-500' : 'text-gray-400')
                  }`} />
                  <span className={`text-sm font-medium ${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  }`}>
                    Baseline Scan
                  </span>
                </div>
                <p className={`text-lg font-semibold ${
                  scanDetails?.isBaseline
                    ? (theme === 'light' ? 'text-green-600' : 'text-green-400')
                    : (theme === 'light' ? 'text-gray-600' : 'text-gray-400')
                }`}>
                  {scanDetails?.isBaseline ? 'Yes' : 'No'}
                </p>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
            <FaFileAlt className="w-full h-full transform rotate-12" />
          </div>
        </div>

      <div className="mb-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
          Player Statistics <span className="text-sm font-normal text-gray-500 dark:text-gray-400">({scanDetails?.players?.length || 0} players)</span>
        </h2>

        {scanDetails?.players?.length ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Player Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Alliance
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Power
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Kill Points
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Dead Troops
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    T4+T5 Kills
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Resources Gathered
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {scanDetails?.players?.map((player, index) => (
                  <tr key={player.governorId || index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {player.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {player.alliance || 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.power)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.killPoints)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.deads)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.t45Kills)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.rssGathered)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'} italic`}>
            No player statistics found for this scan.
          </p>
        )}
      </div>

      {/* Alliance Summary */}
      <div className={`p-6 ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} rounded-lg shadow-md border ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
        <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'} mb-4`}>
          Alliance Summary
        </h2>

        <div className="overflow-x-auto">
          <table className={`min-w-full divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
            <thead className={`${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900'}`}>
              <tr>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Alliance
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Members
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Total Power
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Total Kill Points
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Total Dead Troops
                </th>
              </tr>
            </thead>
            <tbody className={`${theme === 'light' ? 'bg-white divide-y divide-gray-200' : 'bg-gray-800 divide-y divide-gray-700'}`}>
              {Object.entries(
                scanDetails?.players.reduce((acc: Record<string, { members: number; power: number; killPoints: number; deads: number }>, player) => {
                  const alliance = player.alliance || 'Unknown';
                  if (!acc[alliance]) {
                    acc[alliance] = {
                      members: 0,
                      power: 0,
                      killPoints: 0,
                      deads: 0
                    };
                  }
                  acc[alliance].members++;
                  acc[alliance].power += player.power;
                  acc[alliance].killPoints += player.killPoints;
                  acc[alliance].deads += player.deads;
                  return acc;
                }, {} as Record<string, { members: number; power: number; killPoints: number; deads: number }>)
              ).map(([alliance, stats]: [string, { members: number; power: number; killPoints: number; deads: number }]) => (
                <tr key={alliance} className={`${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'} transition-colors duration-150`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-medium ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                      {alliance}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {stats.members}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {formatNumber(stats.power)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {formatNumber(stats.killPoints)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {formatNumber(stats.deads)}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      </div>
    </div>
  );
};

export default ScanDetailPage;