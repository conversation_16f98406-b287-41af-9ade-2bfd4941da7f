"""
Standardized API Response Formats
This module provides consistent response structures across all API endpoints.
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel
from datetime import datetime
from enum import Enum


class ResponseStatus(str, Enum):
    """Standard response status values."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"


class PaginationMeta(BaseModel):
    """Pagination metadata for list responses."""
    page: int = 1
    limit: int = 100
    total: int = 0
    total_pages: int = 0
    has_next: bool = False
    has_prev: bool = False


class ErrorDetail(BaseModel):
    """Detailed error information."""
    code: str
    message: str
    field: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class StandardResponse(BaseModel):
    """Base response format for all API endpoints."""
    status: ResponseStatus
    message: Optional[str] = None
    timestamp: datetime = datetime.utcnow()
    request_id: Optional[str] = None
    data: Optional[Any] = None
    errors: Optional[List[ErrorDetail]] = None
    meta: Optional[Dict[str, Any]] = None


class ListResponse(StandardResponse):
    """Response format for list endpoints with pagination."""
    data: List[Any] = []
    pagination: Optional[PaginationMeta] = None


class SingleResponse(StandardResponse):
    """Response format for single item endpoints."""
    data: Optional[Any] = None


class PerformanceResponse(StandardResponse):
    """Standardized response for performance data."""
    data: Dict[str, Any] = {
        "summary": {},
        "performance_data": [],
        "timeframe": "",
        "scan_info": {}
    }


class DashboardResponse(StandardResponse):
    """Standardized response for dashboard data."""
    data: Dict[str, Any] = {
        "totals": {},
        "gains": {},
        "player_count": 0,
        "scan_info": {},
        "last_updated": None
    }


class KvKResponse(StandardResponse):
    """Standardized response for KvK data."""
    data: Dict[str, Any] = {
        "kvk_info": {},
        "summary": {},
        "performance_data": [],
        "scan_info": {}
    }


def success_response(
    data: Any = None,
    message: str = "Operation completed successfully",
    meta: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized success response."""
    response = StandardResponse(
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data,
        meta=meta,
        request_id=request_id
    )
    return response.dict(exclude_none=True)


def error_response(
    message: str,
    errors: Optional[List[ErrorDetail]] = None,
    status_code: int = 500,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized error response."""
    response = StandardResponse(
        status=ResponseStatus.ERROR,
        message=message,
        errors=errors or [],
        request_id=request_id
    )
    return response.dict(exclude_none=True)


def list_response(
    data: List[Any],
    total: int = None,
    page: int = 1,
    limit: int = 100,
    message: str = "Data retrieved successfully",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized list response with pagination."""
    if total is None:
        total = len(data)
    
    total_pages = (total + limit - 1) // limit if limit > 0 else 1
    
    pagination = PaginationMeta(
        page=page,
        limit=limit,
        total=total,
        total_pages=total_pages,
        has_next=page < total_pages,
        has_prev=page > 1
    )
    
    response = ListResponse(
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data,
        pagination=pagination,
        request_id=request_id
    )
    return response.dict(exclude_none=True)


def performance_response(
    summary: Dict[str, Any],
    performance_data: List[Any],
    timeframe: str = "",
    scan_info: Dict[str, Any] = None,
    message: str = "Performance data retrieved successfully",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized performance response."""
    data = {
        "summary": summary,
        "performance_data": performance_data,
        "timeframe": timeframe,
        "scan_info": scan_info or {}
    }
    
    response = PerformanceResponse(
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data,
        request_id=request_id
    )
    return response.dict(exclude_none=True)


def dashboard_response(
    totals: Dict[str, Any],
    gains: Dict[str, Any],
    player_count: int,
    scan_info: Dict[str, Any],
    last_updated: Optional[datetime] = None,
    message: str = "Dashboard data retrieved successfully",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized dashboard response."""
    data = {
        "totals": totals,
        "gains": gains,
        "player_count": player_count,
        "scan_info": scan_info,
        "last_updated": last_updated.isoformat() if last_updated else None
    }
    
    response = DashboardResponse(
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data,
        request_id=request_id
    )
    return response.dict(exclude_none=True)


def kvk_response(
    kvk_info: Dict[str, Any],
    summary: Dict[str, Any],
    performance_data: List[Any] = None,
    scan_info: Dict[str, Any] = None,
    message: str = "KvK data retrieved successfully",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized KvK response."""
    data = {
        "kvk_info": kvk_info,
        "summary": summary,
        "performance_data": performance_data or [],
        "scan_info": scan_info or {}
    }
    
    response = KvKResponse(
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data,
        request_id=request_id
    )
    return response.dict(exclude_none=True)


def validation_error_response(
    field: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized validation error response."""
    error = ErrorDetail(
        code="VALIDATION_ERROR",
        message=message,
        field=field,
        details=details
    )
    
    return error_response(
        message="Validation failed",
        errors=[error],
        status_code=400,
        request_id=request_id
    )


def not_found_response(
    resource: str,
    identifier: str = "",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized not found response."""
    message = f"{resource} not found"
    if identifier:
        message += f" with identifier: {identifier}"
    
    error = ErrorDetail(
        code="NOT_FOUND",
        message=message,
        details={"resource": resource, "identifier": identifier}
    )
    
    return error_response(
        message=message,
        errors=[error],
        status_code=404,
        request_id=request_id
    )
