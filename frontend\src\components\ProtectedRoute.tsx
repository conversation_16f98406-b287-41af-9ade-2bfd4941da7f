import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useUser } from '../contexts/UserContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission
}) => {
  const { isAuthenticated, hasPermission, user } = useUser();
  const location = useLocation();

  // Debug logging
  console.log('[ProtectedRoute] Checking access:', {
    path: location.pathname,
    isAuthenticated,
    requiredPermission,
    user: user ? { username: user.username, role: user.role } : null,
    hasRequiredPermission: requiredPermission ? hasPermission(requiredPermission) : 'N/A'
  });

  // Check if user is authenticated
  if (!isAuthenticated) {
    console.log('[ProtectedRoute] User not authenticated, redirecting to login');
    // Redirect to login page, but save the location they were trying to access
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If a specific permission is required, check for it
  if (requiredPermission && !hasPermission(requiredPermission)) {
    console.log('[ProtectedRoute] User lacks required permission:', requiredPermission);
    // Redirect to unauthorized page
    return <Navigate to="/unauthorized" replace />;
  }

  console.log('[ProtectedRoute] Access granted, rendering children');
  // If authenticated and has permission, render the children
  return <>{children}</>;
};

export default ProtectedRoute;
