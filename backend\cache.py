import json
import time
from typing import Any, Optional, Dict, Callable
from functools import wraps
import hashlib
import pickle
import os
from logging_config import get_logger

logger = get_logger("cache")

class SimpleCache:
    """Simple in-memory cache with TTL support."""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """Check if cache entry is expired."""
        return time.time() > entry["expires_at"]
    
    def _cleanup_expired(self):
        """Remove expired entries from cache."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time > entry["expires_at"]
        ]
        for key in expired_keys:
            del self.cache[key]
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        if self._is_expired(entry):
            del self.cache[key]
            return None
        
        logger.debug(f"Cache hit for key: {key}")
        return entry["value"]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL."""
        if ttl is None:
            ttl = self.default_ttl
        
        self.cache[key] = {
            "value": value,
            "expires_at": time.time() + ttl,
            "created_at": time.time()
        }
        
        logger.debug(f"Cache set for key: {key}, TTL: {ttl}s")
        
        # Cleanup expired entries periodically
        if len(self.cache) % 100 == 0:
            self._cleanup_expired()
    
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if key in self.cache:
            del self.cache[key]
            logger.debug(f"Cache deleted for key: {key}")
            return True
        return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()
        logger.info("Cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        current_time = time.time()
        active_entries = sum(
            1 for entry in self.cache.values()
            if current_time <= entry["expires_at"]
        )
        
        return {
            "total_entries": len(self.cache),
            "active_entries": active_entries,
            "expired_entries": len(self.cache) - active_entries
        }

# Global cache instance
cache = SimpleCache(default_ttl=300)  # 5 minutes default

def cache_key(*args, **kwargs) -> str:
    """Generate cache key from function arguments."""
    # Create a string representation of all arguments
    key_data = {
        "args": args,
        "kwargs": sorted(kwargs.items())
    }
    
    # Create hash of the key data
    key_string = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_string.encode()).hexdigest()

def cached(ttl: int = 300, key_prefix: str = ""):
    """Decorator to cache function results."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            func_key = f"{key_prefix}{func.__name__}:{cache_key(*args, **kwargs)}"
            
            # Try to get from cache
            cached_result = cache.get(func_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            try:
                result = func(*args, **kwargs)
                cache.set(func_key, result, ttl)
                logger.debug(f"Function result cached: {func.__name__}")
                return result
            except Exception as e:
                logger.error(f"Error in cached function {func.__name__}: {e}")
                raise
        
        # Add cache control methods to the wrapper
        wrapper.cache_clear = lambda: cache.clear()
        wrapper.cache_delete = lambda *args, **kwargs: cache.delete(
            f"{key_prefix}{func.__name__}:{cache_key(*args, **kwargs)}"
        )
        
        return wrapper
    return decorator

# Specific cache decorators for common use cases
def cache_scan_data(ttl: int = 600):  # 10 minutes for scan data
    """Cache decorator specifically for scan data."""
    return cached(ttl=ttl, key_prefix="scan_data:")

def cache_player_data(ttl: int = 300):  # 5 minutes for player data
    """Cache decorator specifically for player data."""
    return cached(ttl=ttl, key_prefix="player_data:")

def cache_kvk_data(ttl: int = 900):  # 15 minutes for KvK data
    """Cache decorator specifically for KvK data."""
    return cached(ttl=ttl, key_prefix="kvk_data:")

def cache_reports(ttl: int = 180):  # 3 minutes for reports
    """Cache decorator specifically for reports."""
    return cached(ttl=ttl, key_prefix="reports:")

# Cache invalidation helpers
def invalidate_scan_cache():
    """Invalidate all scan-related cache entries."""
    keys_to_delete = [key for key in cache.cache.keys() if key.startswith("scan_data:")]
    for key in keys_to_delete:
        cache.delete(key)
    logger.info(f"Invalidated {len(keys_to_delete)} scan cache entries")

def invalidate_player_cache():
    """Invalidate all player-related cache entries."""
    keys_to_delete = [key for key in cache.cache.keys() if key.startswith("player_data:")]
    for key in keys_to_delete:
        cache.delete(key)
    logger.info(f"Invalidated {len(keys_to_delete)} player cache entries")

def invalidate_kvk_cache():
    """Invalidate all KvK-related cache entries."""
    keys_to_delete = [key for key in cache.cache.keys() if key.startswith("kvk_data:")]
    for key in keys_to_delete:
        cache.delete(key)
    logger.info(f"Invalidated {len(keys_to_delete)} KvK cache entries")

def invalidate_reports_cache():
    """Invalidate all report-related cache entries."""
    keys_to_delete = [key for key in cache.cache.keys() if key.startswith("reports:")]
    for key in keys_to_delete:
        cache.delete(key)
    logger.info(f"Invalidated {len(keys_to_delete)} report cache entries")

def invalidate_all_cache():
    """Invalidate all cache entries."""
    cache.clear()
    logger.info("All cache entries invalidated")

# Cache warming functions
def warm_cache():
    """Pre-populate cache with frequently accessed data."""
    logger.info("Starting cache warming...")
    
    try:
        from database import get_db
        import crud
        
        db = next(get_db())
        
        # Warm up basic data
        crud.get_scans(db, limit=10)  # Recent scans
        crud.get_players(db, limit=50)  # Recent players
        
        logger.info("Cache warming completed successfully")
        
    except Exception as e:
        logger.error(f"Cache warming failed: {e}")
    finally:
        if 'db' in locals():
            db.close()

# Cache monitoring
def get_cache_info() -> Dict[str, Any]:
    """Get comprehensive cache information."""
    stats = cache.get_stats()
    
    # Group by prefix
    prefix_stats = {}
    for key in cache.cache.keys():
        prefix = key.split(':')[0] if ':' in key else 'other'
        if prefix not in prefix_stats:
            prefix_stats[prefix] = 0
        prefix_stats[prefix] += 1
    
    return {
        "stats": stats,
        "prefix_breakdown": prefix_stats,
        "memory_usage_estimate": len(str(cache.cache))  # Rough estimate
    }
