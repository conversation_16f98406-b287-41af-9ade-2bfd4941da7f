import React, { useState, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { getGeneralPerformanceSummary } from '../api/api';
import { formatLargeNumber } from '../utils/formatters';
import { useDocumentTitle } from '../hooks/useDocumentTitle';
import ChartCard from '../components/ChartCard';
import {
  FaUsers,
  FaSearch,
  FaFilter,
  FaSort,
  FaChartLine,
  FaShieldAlt,
  FaCrosshairs,
  FaSkullCrossbones,
  FaTrophy,
  FaExclamationTriangle,
  FaArrowUp,
  FaArrowDown,
  FaMinus,
  FaCrown
} from 'react-icons/fa';

interface AllianceData {
  alliance_name: string;
  member_count: number;
  total_power: number;
  avg_power: number;
  total_kp_gain: number;
  avg_kp_gain: number;
  total_deads: number;
  avg_deads: number;
  total_t45_kills: number;
  avg_t45_kills: number;
  efficiency_ratio: number;
  top_performer: string;
  participation_rate: number;
}

const AllianceAnalyticsPage: React.FC = () => {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<keyof AllianceData>('total_kp_gain');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterBy, setFilterBy] = useState<'all' | 'active' | 'large' | 'small'>('all');

  // Set document title
  useDocumentTitle('Alliance Analytics - Kingdom 2358');

  // Fetch player performance data
  const { data: performanceData, isLoading, error } = useQuery({
    queryKey: ['allianceAnalytics'],
    queryFn: () => getGeneralPerformanceSummary(500), // Get all players for alliance analysis
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000,
  });

  // Process alliance data
  const allianceData = useMemo(() => {
    if (!performanceData?.performance_data) return [];

    // Group players by alliance
    const allianceGroups = performanceData.performance_data.reduce((acc: any, player: any) => {
      const alliance = player.alliance || 'No Alliance';
      if (!acc[alliance]) {
        acc[alliance] = [];
      }
      acc[alliance].push(player);
      return acc;
    }, {});

    // Calculate alliance statistics
    const alliances = Object.entries(allianceGroups).map(([allianceName, members]: [string, any]) => {
      const memberCount = members.length;
      const totalPower = members.reduce((sum: number, m: any) => sum + (m.current_power || 0), 0);
      const totalKpGain = members.reduce((sum: number, m: any) => sum + (m.kp_delta || 0), 0);
      const totalDeads = members.reduce((sum: number, m: any) => sum + (m.deads_delta || 0), 0);
      const totalT45Kills = members.reduce((sum: number, m: any) => sum + (m.t45_kills_delta || 0), 0);

      const activeMembers = members.filter((m: any) => (m.kp_delta || 0) > 0).length;
      const participationRate = memberCount > 0 ? (activeMembers / memberCount) * 100 : 0;

      const topPerformer = members.reduce((top: any, current: any) => {
        return (current.kp_delta || 0) > (top.kp_delta || 0) ? current : top;
      }, members[0]);

      return {
        alliance_name: allianceName,
        member_count: memberCount,
        total_power: totalPower,
        avg_power: memberCount > 0 ? totalPower / memberCount : 0,
        total_kp_gain: totalKpGain,
        avg_kp_gain: memberCount > 0 ? totalKpGain / memberCount : 0,
        total_deads: totalDeads,
        avg_deads: memberCount > 0 ? totalDeads / memberCount : 0,
        total_t45_kills: totalT45Kills,
        avg_t45_kills: memberCount > 0 ? totalT45Kills / memberCount : 0,
        efficiency_ratio: totalDeads > 0 ? totalKpGain / totalDeads : 0,
        top_performer: topPerformer?.player_name || 'Unknown',
        participation_rate: participationRate
      };
    });

    return alliances;
  }, [performanceData]);

  // Filter and sort alliances
  const processedAlliances = useMemo(() => {
    let filtered = [...allianceData];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(alliance =>
        alliance.alliance_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alliance.top_performer.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'active':
        filtered = filtered.filter(alliance => alliance.participation_rate >= 50);
        break;
      case 'large':
        filtered = filtered.filter(alliance => alliance.member_count >= 20);
        break;
      case 'small':
        filtered = filtered.filter(alliance => alliance.member_count < 20 && alliance.member_count > 1);
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      const multiplier = sortOrder === 'desc' ? -1 : 1;

      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return aVal.localeCompare(bVal) * multiplier;
      }
      return ((aVal as number) - (bVal as number)) * multiplier;
    });

    return filtered;
  }, [allianceData, searchTerm, sortBy, sortOrder, filterBy]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!allianceData.length) return null;

    const totalAlliances = allianceData.length;
    const totalMembers = allianceData.reduce((sum, a) => sum + a.member_count, 0);
    const avgAllianceSize = totalMembers / totalAlliances;
    const topAlliance = allianceData.reduce((top, current) =>
      current.total_kp_gain > top.total_kp_gain ? current : top
    );

    return {
      totalAlliances,
      totalMembers,
      avgAllianceSize,
      topAlliance: topAlliance.alliance_name,
      topAllianceKp: topAlliance.total_kp_gain
    };
  }, [allianceData]);

  // Chart data for alliance distribution
  const allianceSizeDistribution = useMemo(() => {
    if (!allianceData.length) return [];

    return [
      { name: 'Large (20+)', value: allianceData.filter(a => a.member_count >= 20).length },
      { name: 'Medium (10-19)', value: allianceData.filter(a => a.member_count >= 10 && a.member_count < 20).length },
      { name: 'Small (5-9)', value: allianceData.filter(a => a.member_count >= 5 && a.member_count < 10).length },
      { name: 'Tiny (1-4)', value: allianceData.filter(a => a.member_count >= 1 && a.member_count < 5).length }
    ];
  }, [allianceData]);

  const handleSort = (field: keyof AllianceData) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const getSortIcon = (field: keyof AllianceData) => {
    if (sortBy !== field) return <FaSort className="opacity-50" />;
    return sortOrder === 'desc' ? <FaArrowDown /> : <FaArrowUp />;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading alliance analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <FaExclamationTriangle className="text-4xl mx-auto mb-4" />
          <p>Error loading alliance analytics</p>
          <p className="text-sm mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaUsers className="text-2xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Alliance Analytics
                </h1>
                <p className={`text-lg mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Comprehensive alliance performance analysis and rankings
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Statistics */}
        {summaryStats && (
          <section className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Total Alliances
                    </p>
                    <p className={`text-3xl font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {summaryStats.totalAlliances}
                    </p>
                  </div>
                  <FaUsers className={`text-2xl ${
                    theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                  }`} />
                </div>
              </div>

              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Total Members
                    </p>
                    <p className={`text-3xl font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {summaryStats.totalMembers}
                    </p>
                    <p className="text-sm text-blue-600">
                      Avg: {summaryStats.avgAllianceSize.toFixed(1)} per alliance
                    </p>
                  </div>
                  <FaShieldAlt className="text-2xl text-green-600" />
                </div>
              </div>

              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Top Alliance
                    </p>
                    <p className={`text-lg font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {summaryStats.topAlliance}
                    </p>
                    <p className="text-sm text-yellow-600">
                      {formatLargeNumber(summaryStats.topAllianceKp)} KP
                    </p>
                  </div>
                  <FaCrown className="text-2xl text-yellow-600" />
                </div>
              </div>

              <div className={`p-6 rounded-2xl shadow-xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Showing Results
                    </p>
                    <p className={`text-3xl font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {processedAlliances.length}
                    </p>
                  </div>
                  <FaFilter className="text-2xl text-purple-600" />
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Alliance Size Distribution Chart */}
        <section className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartCard
              title="Alliance Size Distribution"
              data={allianceSizeDistribution}
              type="pie"
            />
            <ChartCard
              title="Top Alliances by Kill Points"
              data={processedAlliances.slice(0, 10).map(alliance => ({
                name: alliance.alliance_name,
                value: alliance.total_kp_gain
              }))}
              type="bar"
            />
          </div>
        </section>

        {/* Filters and Search */}
        <section className="mb-8">
          <div className={`p-6 rounded-2xl shadow-xl ${
            theme === 'light' ? 'bg-white' : 'bg-gray-800'
          }`}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search alliances or top performers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                    theme === 'light'
                      ? 'bg-gray-50 border-gray-300 text-gray-900'
                      : 'bg-gray-700 border-gray-600 text-white'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
              </div>

              {/* Filter */}
              <div className="relative">
                <FaFilter className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                  className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                    theme === 'light'
                      ? 'bg-gray-50 border-gray-300 text-gray-900'
                      : 'bg-gray-700 border-gray-600 text-white'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                >
                  <option value="all">All Alliances</option>
                  <option value="active">Active Alliances (50%+ participation)</option>
                  <option value="large">Large Alliances (20+ members)</option>
                  <option value="small">Small Alliances (2-19 members)</option>
                </select>
              </div>

              {/* Results Count */}
              <div className="flex items-center justify-center">
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  Showing {processedAlliances.length} alliances
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Alliance Table */}
        <section>
          <div className={`rounded-2xl shadow-xl overflow-hidden ${
            theme === 'light' ? 'bg-white' : 'bg-gray-800'
          }`}>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${
                  theme === 'light' ? 'bg-gray-50' : 'bg-gray-700'
                }`}>
                  <tr>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('alliance_name')}>
                      <div className="flex items-center space-x-1">
                        <span>Alliance</span>
                        {getSortIcon('alliance_name')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('member_count')}>
                      <div className="flex items-center space-x-1">
                        <span>Members</span>
                        {getSortIcon('member_count')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('total_kp_gain')}>
                      <div className="flex items-center space-x-1">
                        <span>Total KP</span>
                        {getSortIcon('total_kp_gain')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('avg_kp_gain')}>
                      <div className="flex items-center space-x-1">
                        <span>Avg KP</span>
                        {getSortIcon('avg_kp_gain')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('participation_rate')}>
                      <div className="flex items-center space-x-1">
                        <span>Participation</span>
                        {getSortIcon('participation_rate')}
                      </div>
                    </th>
                    <th className={`px-6 py-4 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-300'
                    }`} onClick={() => handleSort('top_performer')}>
                      <div className="flex items-center space-x-1">
                        <span>Top Performer</span>
                        {getSortIcon('top_performer')}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className={`divide-y ${
                  theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'
                }`}>
                  {processedAlliances.map((alliance, index) => (
                    <tr key={alliance.alliance_name} className={`hover:bg-opacity-50 ${
                      theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'
                    }`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white mr-3 ${
                            index < 3 ? 'bg-yellow-500' : 'bg-gray-500'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <div className={`text-sm font-medium ${
                              theme === 'light' ? 'text-gray-900' : 'text-white'
                            }`}>
                              {alliance.alliance_name}
                            </div>
                            <div className={`text-xs ${
                              theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              Power: {formatLargeNumber(alliance.total_power)}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          theme === 'light' ? 'text-gray-900' : 'text-white'
                        }`}>
                          {alliance.member_count}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          alliance.total_kp_gain > 0 ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {formatLargeNumber(alliance.total_kp_gain)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          alliance.avg_kp_gain > 0 ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {formatLargeNumber(alliance.avg_kp_gain)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`w-16 bg-gray-200 rounded-full h-2 mr-2 ${
                            theme === 'light' ? 'bg-gray-200' : 'bg-gray-600'
                          }`}>
                            <div
                              className={`h-2 rounded-full ${
                                alliance.participation_rate >= 75 ? 'bg-green-500' :
                                alliance.participation_rate >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(alliance.participation_rate, 100)}%` }}
                            ></div>
                          </div>
                          <span className={`text-xs font-medium ${
                            alliance.participation_rate >= 75 ? 'text-green-600' :
                            alliance.participation_rate >= 50 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {alliance.participation_rate.toFixed(0)}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          theme === 'light' ? 'text-gray-900' : 'text-white'
                        }`}>
                          {alliance.top_performer}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {processedAlliances.length === 0 && (
              <div className="text-center py-12">
                <FaUsers className={`text-4xl mx-auto mb-4 ${
                  theme === 'light' ? 'text-gray-400' : 'text-gray-600'
                }`} />
                <p className={`text-lg font-medium ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  No alliances found
                </p>
                <p className={`text-sm ${
                  theme === 'light' ? 'text-gray-500' : 'text-gray-500'
                }`}>
                  Try adjusting your search or filter criteria
                </p>
              </div>
            )}
          </div>
        </section>
      </div>
    </div>
  );
};

export default AllianceAnalyticsPage;