/**
 * Performance Optimization Hooks
 * Provides React performance optimizations including memoization, virtualization, and lazy loading
 */

import { useMemo, useCallback, useRef, useEffect, useState } from 'react';
import { debounce, throttle } from 'lodash';

/**
 * Hook for optimized data processing with memoization
 */
export const useOptimizedData = <T, R>(
  data: T[],
  processor: (data: T[]) => R,
  dependencies: any[] = []
): R => {
  return useMemo(() => {
    if (!data || data.length === 0) {
      return processor([]);
    }
    
    console.log(`[Performance] Processing ${data.length} items`);
    const startTime = performance.now();
    
    const result = processor(data);
    
    const endTime = performance.now();
    console.log(`[Performance] Data processing took ${(endTime - startTime).toFixed(2)}ms`);
    
    return result;
  }, [data, ...dependencies]);
};

/**
 * Hook for debounced search/filter operations
 */
export const useDebouncedSearch = (
  searchTerm: string,
  delay: number = 300
): string => {
  const [debouncedValue, setDebouncedValue] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(searchTerm);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm, delay]);

  return debouncedValue;
};

/**
 * Hook for throttled scroll events
 */
export const useThrottledScroll = (
  callback: (event: Event) => void,
  delay: number = 100
) => {
  const throttledCallback = useCallback(
    throttle(callback, delay),
    [callback, delay]
  );

  useEffect(() => {
    window.addEventListener('scroll', throttledCallback);
    return () => {
      window.removeEventListener('scroll', throttledCallback);
      throttledCallback.cancel();
    };
  }, [throttledCallback]);
};

/**
 * Hook for virtual scrolling large lists
 */
export const useVirtualScroll = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    handleScroll,
    totalHeight: visibleItems.totalHeight
  };
};

/**
 * Hook for lazy loading images
 */
export const useLazyImage = (src: string, placeholder?: string) => {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setImageSrc(src);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [src]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  return { imageSrc, isLoaded, imgRef, handleLoad };
};

/**
 * Hook for optimized sorting with memoization
 */
export const useOptimizedSort = <T>(
  data: T[],
  sortKey: keyof T,
  sortDirection: 'asc' | 'desc' = 'desc'
): T[] => {
  return useMemo(() => {
    if (!data || data.length === 0) return [];
    
    console.log(`[Performance] Sorting ${data.length} items by ${String(sortKey)}`);
    const startTime = performance.now();
    
    const sorted = [...data].sort((a, b) => {
      const aValue = a[sortKey];
      const bValue = b[sortKey];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'desc' ? bValue - aValue : aValue - bValue;
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortDirection === 'desc' ? -comparison : comparison;
      }
      
      return 0;
    });
    
    const endTime = performance.now();
    console.log(`[Performance] Sorting took ${(endTime - startTime).toFixed(2)}ms`);
    
    return sorted;
  }, [data, sortKey, sortDirection]);
};

/**
 * Hook for optimized filtering with memoization
 */
export const useOptimizedFilter = <T>(
  data: T[],
  filterFn: (item: T) => boolean,
  dependencies: any[] = []
): T[] => {
  return useMemo(() => {
    if (!data || data.length === 0) return [];
    
    console.log(`[Performance] Filtering ${data.length} items`);
    const startTime = performance.now();
    
    const filtered = data.filter(filterFn);
    
    const endTime = performance.now();
    console.log(`[Performance] Filtering took ${(endTime - startTime).toFixed(2)}ms, ${filtered.length} items remaining`);
    
    return filtered;
  }, [data, filterFn, ...dependencies]);
};

/**
 * Hook for performance monitoring
 */
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(performance.now());

  useEffect(() => {
    renderCount.current += 1;
    const currentTime = performance.now();
    const timeSinceLastRender = currentTime - lastRenderTime.current;
    
    console.log(`[Performance] ${componentName} render #${renderCount.current}, ${timeSinceLastRender.toFixed(2)}ms since last render`);
    
    lastRenderTime.current = currentTime;
  });

  const logPerformance = useCallback((operation: string, duration: number) => {
    console.log(`[Performance] ${componentName} - ${operation}: ${duration.toFixed(2)}ms`);
  }, [componentName]);

  return { renderCount: renderCount.current, logPerformance };
};

/**
 * Hook for chunked data processing to prevent UI blocking
 */
export const useChunkedProcessing = <T, R>(
  data: T[],
  processor: (chunk: T[]) => R[],
  chunkSize: number = 100
): { results: R[]; isProcessing: boolean; progress: number } => {
  const [results, setResults] = useState<R[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!data || data.length === 0) {
      setResults([]);
      setProgress(0);
      return;
    }

    setIsProcessing(true);
    setResults([]);
    setProgress(0);

    const processChunk = (startIndex: number) => {
      const chunk = data.slice(startIndex, startIndex + chunkSize);
      if (chunk.length === 0) {
        setIsProcessing(false);
        setProgress(100);
        return;
      }

      // Process chunk asynchronously to prevent UI blocking
      setTimeout(() => {
        const chunkResults = processor(chunk);
        setResults(prev => [...prev, ...chunkResults]);
        
        const newProgress = Math.min(((startIndex + chunkSize) / data.length) * 100, 100);
        setProgress(newProgress);
        
        if (startIndex + chunkSize < data.length) {
          processChunk(startIndex + chunkSize);
        } else {
          setIsProcessing(false);
        }
      }, 0);
    };

    processChunk(0);
  }, [data, processor, chunkSize]);

  return { results, isProcessing, progress };
};

/**
 * Hook for optimized table rendering with pagination
 */
export const useOptimizedTable = <T>(
  data: T[],
  pageSize: number = 50
) => {
  const [currentPage, setCurrentPage] = useState(1);
  
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, currentPage, pageSize]);

  const totalPages = Math.ceil(data.length / pageSize);

  const goToPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  }, [totalPages]);

  const nextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  const prevPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  return {
    paginatedData,
    currentPage,
    totalPages,
    goToPage,
    nextPage,
    prevPage,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1
  };
};

/**
 * Hook for memory usage monitoring
 */
export const useMemoryMonitor = (componentName: string) => {
  useEffect(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log(`[Memory] ${componentName} - Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB, Total: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
    }
  });
};

/**
 * Hook for optimized number formatting
 */
export const useNumberFormatter = () => {
  const formatNumber = useCallback((num: number): string => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    }
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toLocaleString();
  }, []);

  const formatPercentage = useCallback((num: number): string => {
    return `${(num * 100).toFixed(1)}%`;
  }, []);

  return { formatNumber, formatPercentage };
};
