import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import {
  FaShieldAlt,
  FaExclamationTriangle,
  FaHome,
  FaLock,
  FaCrown
} from 'react-icons/fa';

const UnauthorizedPage: React.FC = () => {
  const { theme } = useTheme();

  return (
    <div className={`min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${
      theme === 'light'
        ? 'bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50'
        : 'bg-gradient-to-br from-gray-900 via-red-900 to-orange-900'
    }`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full opacity-20 ${
          theme === 'light' ? 'bg-red-300' : 'bg-red-600'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full opacity-20 ${
          theme === 'light' ? 'bg-orange-300' : 'bg-orange-600'
        }`}></div>
      </div>

      <div className="relative z-10 max-w-lg w-full">
        {/* Enhanced Error Card */}
        <div className={`rounded-3xl shadow-2xl overflow-hidden ${
          theme === 'light' ? 'bg-white/90 backdrop-blur-sm' : 'bg-gray-800/90 backdrop-blur-sm'
        }`}>
          {/* Header */}
          <div className={`px-8 py-12 text-center ${
            theme === 'light'
              ? 'bg-gradient-to-r from-red-600 to-orange-600'
              : 'bg-gradient-to-r from-red-700 to-orange-700'
          }`}>
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-white/20 backdrop-blur-sm">
                <FaShieldAlt className="text-4xl text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Access Restricted
            </h1>
            <p className="text-red-100 text-lg">
              Kingdom 2358 Security Protocol
            </p>
          </div>

          {/* Content */}
          <div className="px-8 py-12 text-center">
            {/* Lock Icon */}
            <div className={`mx-auto w-24 h-24 rounded-full flex items-center justify-center mb-8 ${
              theme === 'light'
                ? 'bg-red-100 text-red-600'
                : 'bg-red-900/30 text-red-400'
            }`}>
              <FaLock className="text-4xl" />
            </div>

            <h2 className={`text-2xl font-bold mb-4 ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              Insufficient Permissions
            </h2>

            <p className={`text-lg mb-8 ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-300'
            }`}>
              You don't have the required permissions to access this area of the kingdom's data portal.
            </p>

            {/* Warning Box */}
            <div className={`p-4 rounded-xl border-l-4 mb-8 ${
              theme === 'light'
                ? 'bg-yellow-50 border-yellow-400 text-yellow-800'
                : 'bg-yellow-900/20 border-yellow-500 text-yellow-300'
            }`}>
              <div className="flex items-center">
                <FaExclamationTriangle className="mr-3 text-lg" />
                <div className="text-left">
                  <p className="font-semibold">Contact your alliance leader</p>
                  <p className="text-sm mt-1">Request elevated permissions if you need access to this feature.</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <Link
                to="/"
                className={`group relative w-full inline-flex items-center justify-center px-6 py-4 rounded-xl font-semibold text-white transition-all duration-300 transform hover:scale-105 ${
                  theme === 'light'
                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700'
                    : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600'
                } shadow-lg hover:shadow-xl`}
              >
                <FaHome className="mr-3 text-lg group-hover:animate-pulse" />
                Return to Dashboard
                <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
              </Link>

              <div className="flex items-center justify-center">
                <FaCrown className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'
                }`} />
                <span className={`text-sm ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  Kingdom 2358 • Asahikawa
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
