# Node.js / Frontend
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm
.yarn-integrity
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vite / React build outputs
/frontend/dist/
/frontend/build/
/frontend/.vite/
/frontend/coverage/

# Python / Backend
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
/backend/env/
/backend/venv/
/backend/.env
/backend/.venv
/backend/ENV/
/backend/env.bak/
/backend/venv.bak/
/backend/.python-version
/backend/pip-log.txt
/backend/pip-delete-this-directory.txt
/backend/.coverage
/backend/htmlcov/
/backend/.pytest_cache/
/backend/.coverage.*

# Database files
*.db
*.sqlite
*.sqlite3
kvk_tracker.db

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Logs
logs
*.log

# Environment variables
.env
.env.*
!.env.example

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IDE - JetBrains (PyCharm, IntelliJ, etc)
.idea/
*.iml
*.iws
*.ipr
.idea_modules/
out/

# IDE - Eclipse
.classpath
.project
.settings/

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory
.Trash-*

# Temporary files
*.swp
*.swo
*~
tmp/
temp/

# Docker
.dockerignore
docker-compose.override.yml

# Misc
.cache/
.parcel-cache/
.next/
.nuxt/
.vercel
.coverage
coverage/
