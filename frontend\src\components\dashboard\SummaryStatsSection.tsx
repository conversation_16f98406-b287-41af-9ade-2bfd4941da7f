import React, { memo } from 'react'; // Import memo
import StatCard from '../StatCard'; // Assuming StatCard is in components directory
import { useTheme } from '../../contexts/ThemeContext';
import { FaShieldAlt, FaFistRaised, FaSkullCrossbones } from 'react-icons/fa';
// import { KingdomData } from '../../types/dataTypes'; // Type not needed for current implementation

interface SummaryStatsSectionProps {
  kingdomData: any; // Using any for now - can be typed properly later
  formatter: (num: number, abbreviated?: boolean) => string;
}

const SummaryStatsSection: React.FC<SummaryStatsSectionProps> = ({ kingdomData, formatter }) => {
  const { theme } = useTheme();
  return (
    <section className="mb-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="Total Power"
        value={kingdomData.totalPower}
        icon={<FaShieldAlt className="h-6 w-6" />}
        formatter={(val) => formatter(val, true)}
      />
      <StatCard
        title="Kill Points"
        value={kingdomData.totalKillPoints}
        icon={<FaFistRaised className="h-6 w-6" />}
        formatter={(val) => formatter(val, true)}
      />
      <StatCard
        title="Dead Troops"
        value={kingdomData.totalDeads}
        icon={<FaSkullCrossbones className="h-6 w-6" />}
        formatter={(val) => formatter(val, true)}
      />
      <StatCard
        title="Active Players"
        value={`${kingdomData.activePlayers}/${kingdomData.totalPlayers}`}
        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" /></svg>}
      />
    </section>
  );
};

export default memo(SummaryStatsSection); // Wrap with memo
