import React, { memo, useMemo, useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';
import { FaTrophy, FaMedal, FaAward, FaSort, FaSortUp, FaSortDown } from 'react-icons/fa';

interface LeaderboardProps {
  title: string;
  players: PlayerScanData[];
  valueKey: keyof PlayerScanData;
  valueFormatter?: (value: number) => string;
  enableSorting?: boolean;
}

const Leaderboard: React.FC<LeaderboardProps> = ({
  title,
  players,
  valueKey,
  valueFormatter = (value: number) => value.toLocaleString(),
  enableSorting = true
}) => {
  const { theme } = useTheme();
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Sort players by the value key
  const sortedPlayers = useMemo(() => {
    if (!players || players.length === 0) return [];

    return [...players].sort((a, b) => {
      const aValue = a[valueKey] as number;
      const bValue = b[valueKey] as number;

      if (sortDirection === 'desc') {
        return (bValue || 0) - (aValue || 0); // Highest values first
      } else {
        return (aValue || 0) - (bValue || 0); // Lowest values first
      }
    });
  }, [players, valueKey, sortDirection]);

  // Debug logging
  console.log(`Leaderboard "${title}":`, {
    playersCount: players?.length || 0,
    valueKey,
    sortDirection,
    firstPlayer: sortedPlayers?.[0],
    firstPlayerValue: sortedPlayers?.[0]?.[valueKey]
  });

  const handleSort = () => {
    if (enableSorting) {
      setSortDirection(prev => prev === 'desc' ? 'asc' : 'desc');
    }
  };

  const getSortIcon = () => {
    if (!enableSorting) return null;

    if (sortDirection === 'desc') {
      return <FaSortDown className="text-sm" />;
    } else {
      return <FaSortUp className="text-sm" />;
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <FaTrophy className="text-yellow-500" />;
      case 2:
        return <FaMedal className="text-gray-400" />;
      case 3:
        return <FaAward className="text-amber-600" />;
      default:
        return <span className={`font-bold ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>{rank}</span>;
    }
  };

  return (
    <div className={`rounded-2xl overflow-hidden shadow-2xl transition-all duration-300 hover:shadow-3xl ${
      theme === 'light' ? 'bg-white' : 'bg-gray-800'
    }`}>
      {/* Enhanced Header with Sorting */}
      <div className={`px-6 py-4 ${
        theme === 'light'
          ? 'bg-gradient-to-r from-blue-600 to-indigo-600'
          : 'bg-gradient-to-r from-blue-700 to-indigo-700'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-white/20 backdrop-blur-sm mr-3">
              <FaTrophy className="text-lg text-white" />
            </div>
            <h3 className="text-xl font-bold text-white">{title}</h3>
          </div>
          {enableSorting && (
            <button
              onClick={handleSort}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-all duration-200 text-white"
              title={`Sort ${sortDirection === 'desc' ? 'ascending' : 'descending'}`}
            >
              <span className="text-sm font-medium">
                {sortDirection === 'desc' ? 'Highest' : 'Lowest'}
              </span>
              {getSortIcon()}
            </button>
          )}
        </div>
      </div>
      {/* Enhanced Player Cards */}
      <div className="p-6 space-y-3">
        {!players || players.length === 0 ? (
          <div className={`text-center py-8 ${
            theme === 'light' ? 'text-gray-500' : 'text-gray-400'
          }`}>
            <FaTrophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-semibold">No data available</p>
            <p className="text-sm">Check back after uploading scan data</p>
          </div>
        ) : (
          sortedPlayers.map((player, index) => {
            const rank = index + 1;
            const isTopThree = rank <= 3;
            const value = player[valueKey] as number;

            // Skip players with invalid data
            if (!player || !player.name || value === undefined || value === null) {
              return null;
            }

            return (
              <div
                key={player.governorId || index}
                className={`group relative overflow-hidden rounded-xl p-4 transition-all duration-300 hover:scale-102 ${
                  isTopThree
                    ? theme === 'light'
                      ? 'bg-gradient-to-r from-yellow-50 via-amber-50 to-orange-50 border-2 border-yellow-200 hover:border-yellow-300'
                      : 'bg-gradient-to-r from-yellow-900/20 via-amber-900/20 to-orange-900/20 border-2 border-yellow-700 hover:border-yellow-600'
                    : theme === 'light'
                      ? 'bg-gray-50 hover:bg-white border border-gray-200 hover:border-gray-300'
                      : 'bg-gray-700 hover:bg-gray-600 border border-gray-600 hover:border-gray-500'
                } shadow-lg hover:shadow-xl`}
              >
              {/* Rank Badge */}
              <div className="absolute top-2 left-2">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  isTopThree
                    ? 'bg-gradient-to-br from-yellow-400 to-orange-500 text-white'
                    : theme === 'light'
                      ? 'bg-gray-200 text-gray-700'
                      : 'bg-gray-600 text-gray-300'
                }`}>
                  {rank <= 3 ? getRankIcon(rank) : rank}
                </div>
              </div>

              <div className="flex items-center justify-between pl-12">
                {/* Player Info */}
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h4 className={`text-lg font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      {player.name}
                    </h4>

                    {/* Alliance Badge */}
                    <span
                      className="px-3 py-1 text-xs rounded-full font-semibold"
                      style={{
                        backgroundColor: getAllianceColor(player.alliance || 'N/A', theme === 'light' ? 0.2 : 0.3),
                        color: getAllianceColor(player.alliance || 'N/A', 1)
                      }}
                    >
                      {player.alliance || 'No Alliance'}
                    </span>
                  </div>

                  {/* Value Label */}
                  <p className={`text-sm mt-1 ${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {valueKey === 'power' ? 'Power' :
                     valueKey === 'killPoints' ? 'Kill Points' :
                     valueKey === 'deads' ? 'Dead Troops' :
                     valueKey === 't1Kills' ? 'T1 Kills' :
                     valueKey === 't2Kills' ? 'T2 Kills' :
                     valueKey === 't3Kills' ? 'T3 Kills' :
                     valueKey === 't4Kills' ? 'T4 Kills' :
                     valueKey === 't5Kills' ? 'T5 Kills' :
                     valueKey === 'totalKills' ? 'Total Kills' :
                     valueKey === 't45Kills' ? 'T4-5 Kills' :
                     valueKey === 'ranged' ? 'Ranged' :
                     valueKey === 'rssGathered' ? 'RSS Gathered' :
                     valueKey === 'rssAssisted' ? 'RSS Assisted' :
                     valueKey === 'helps' ? 'Helps' :
                     String(valueKey)}
                  </p>
                </div>

                {/* Value Display */}
                <div className="text-right">
                  <p className={`text-2xl font-bold ${
                    isTopThree
                      ? 'text-orange-600'
                      : theme === 'light'
                        ? 'text-blue-600'
                        : 'text-blue-400'
                  }`}>
                    {valueFormatter(value)}
                  </p>
                </div>
              </div>

              {/* Glow effect for top 3 */}
              {isTopThree && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-400/10 to-orange-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
            </div>
          );
        }).filter(Boolean))}
      </div>
    </div>
  );
};

// Helper function to get a consistent color for each alliance
const getAllianceColor = (alliance: string, opacity: number = 1): string => {
  const colors: Record<string, string> = {
    'ASAH': `rgba(59, 130, 246, ${opacity})`, // blue
    'KAWA': `rgba(16, 185, 129, ${opacity})`, // green
    'HOKU': `rgba(245, 158, 11, ${opacity})`, // amber
    'NIJI': `rgba(236, 72, 153, ${opacity})`, // pink
    'YUKI': `rgba(139, 92, 246, ${opacity})`, // purple
    'KAZE': `rgba(239, 68, 68, ${opacity})`, // red
  };

  return colors[alliance] || `rgba(107, 114, 128, ${opacity})`; // gray default
};

export default memo(Leaderboard);
