import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getScans } from '../api/api'; // Use the updated API function
import { getKvKList } from '../services/kvkService'; // Added: Assuming getKvKList returns KvKData[]
import { ScanData, KvKData } from '../types/dataTypes'; // Added KvKData, ensured Scan type is available
import { useTheme } from '../contexts/ThemeContext'; // Theme context no longer needed for direct styling
import {
  FaUpload,
  FaEye,
  FaCalendarAlt,
  FaUsers,
  FaCheckCircle,
  FaTimesCircle,
  FaFileAlt,
  FaChartLine,
  FaExclamationTriangle
} from 'react-icons/fa';

const ScansListPage: React.FC = () => {
  const { theme } = useTheme();

  const {
    data: scans = [],
    isLoading: isLoadingScans,
    error: errorScans,
    refetch: refetchScans
  } = useQuery<ScanData[], Error>({
    queryKey: ['scansList'],
    queryFn: getScans,
    retry: 1,
    refetchOnWindowFocus: false
  });

  const {
    data: kvkList = [],
    isLoading: isLoadingKvK,
    error: errorKvK,
    refetch: refetchKvKs
  } = useQuery<KvKData[], Error>({
    queryKey: ['kvkList'],
    queryFn: getKvKList,
    retry: 1,
    refetchOnWindowFocus: false
  });

  if (isLoadingScans || isLoadingKvK) return (
    <div className="text-center py-10 text-gray-700 dark:text-gray-300">
      <div className="animate-pulse">Loading data...</div>
    </div>
  );

  if (errorScans || errorKvK) {
    const combinedError = errorScans || errorKvK;
    const refetchAll = () => {
      if (errorScans) refetchScans();
      if (errorKvK) refetchKvKs();
    };
    return (
      <div className="text-center py-10 bg-red-50 text-red-600 rounded-lg p-4 max-w-2xl mx-auto border border-red-200">
        <p className="font-semibold">Error fetching data:</p>
        <p>{(combinedError as Error).message}</p>
        <button
          onClick={refetchAll}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  // Process and group scans
  const scansByKvkId: Record<string, ScanData[]> = {};
  const unassignedScans: ScanData[] = [];
  const kvkMap = new Map(kvkList.map(kvk => [kvk.id, kvk]));

  scans.forEach(scan => {
    // Check if scan has a kvkId and if that KvK exists
    if (scan.kvkId && kvkMap.has(scan.kvkId)) {
      if (!scansByKvkId[scan.kvkId]) {
        scansByKvkId[scan.kvkId] = [];
      }
      scansByKvkId[scan.kvkId].push(scan);
    } else {
      unassignedScans.push(scan);
    }
  });

  const sortedKvKList = [...kvkList].sort((a, b) => (b.season || 0) - (a.season || 0) || a.name.localeCompare(b.name));

  const noDataAvailable = sortedKvKList.length === 0 && unassignedScans.length === 0;

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600'
            : 'bg-gradient-to-r from-indigo-800 via-purple-800 to-pink-800'
        } shadow-2xl`}>
          <div className="relative px-8 py-12">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="flex items-center mb-6 md:mb-0">
                <div className="p-4 rounded-2xl bg-white/20 backdrop-blur-sm mr-6">
                  <FaFileAlt className="text-4xl text-white" />
                </div>
                <div>
                  <h1 className="text-5xl font-bold text-white mb-2">
                    Scan Archive
                  </h1>
                  <p className="text-xl text-purple-100">
                    Browse all uploaded scan data organized by KvK
                  </p>
                </div>
              </div>

              <Link
                to="/upload-scan"
                className="group relative inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm text-white font-semibold rounded-2xl hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
              >
                <FaUpload className="mr-3 text-lg group-hover:animate-bounce" />
                Upload New Scan
                <div className="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
              </Link>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 opacity-20">
            <FaChartLine className="w-full h-full transform rotate-12" />
          </div>
        </div>

      {noDataAvailable && scans.length === 0 ? (
        <div className={`text-center py-16 px-8 rounded-3xl shadow-2xl ${
          theme === 'light' ? 'bg-white' : 'bg-gray-800'
        }`}>
          <div className={`mx-auto w-24 h-24 rounded-full flex items-center justify-center mb-6 ${
            theme === 'light' ? 'bg-blue-100' : 'bg-blue-900'
          }`}>
            <FaExclamationTriangle className={`text-4xl ${
              theme === 'light' ? 'text-blue-600' : 'text-blue-400'
            }`} />
          </div>
          <h3 className={`text-2xl font-bold mb-4 ${
            theme === 'light' ? 'text-gray-900' : 'text-gray-100'
          }`}>
            No Scans Available
          </h3>
          <p className={`text-lg mb-8 max-w-md mx-auto ${
            theme === 'light' ? 'text-gray-600' : 'text-gray-300'
          }`}>
            Start tracking your kingdom's performance by uploading your first scan data.
          </p>
          <Link
            to="/upload-scan"
            className={`group relative inline-flex items-center px-8 py-4 rounded-2xl font-semibold text-white transition-all duration-300 transform hover:scale-105 ${
              theme === 'light'
                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700'
                : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600'
            } shadow-xl hover:shadow-2xl`}
          >
            <FaUpload className="mr-3 text-lg group-hover:animate-bounce" />
            Upload Your First Scan
            <div className="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
          </Link>
        </div>
      ) : noDataAvailable && scans.length > 0 ? (
        <div className="text-center py-10 text-gray-700 dark:text-gray-300">
            <p>No KvKs found, or existing scans are not yet associated with any KvK.</p>
        </div>
      ) : (
        <>
          {sortedKvKList.map(kvk => {
            const currentKvkScans = scansByKvkId[kvk.id] || [];
            return (
              <div key={kvk.id} className={`mb-8 rounded-3xl shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-3xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                {/* KvK Header */}
                <div className={`p-6 ${
                  theme === 'light'
                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600'
                    : 'bg-gradient-to-r from-blue-700 to-indigo-700'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="p-3 rounded-xl bg-white/20 backdrop-blur-sm mr-4">
                        <FaFileAlt className="text-2xl text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-white">
                          {kvk.name}
                        </h2>
                        <p className="text-blue-100">
                          Season {kvk.season} • {currentKvkScans.length} scan{currentKvkScans.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className={`px-4 py-2 rounded-xl text-sm font-semibold ${
                      kvk.status === 'active'
                        ? 'bg-green-500/20 text-green-100'
                        : kvk.status === 'completed'
                        ? 'bg-gray-500/20 text-gray-100'
                        : 'bg-yellow-500/20 text-yellow-100'
                    }`}>
                      {kvk.status?.charAt(0).toUpperCase() + kvk.status?.slice(1) || 'Unknown'}
                    </div>
                  </div>
                </div>
                {/* Scan Cards */}
                {currentKvkScans.length > 0 ? (
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {currentKvkScans.map((scan) => (
                        <div key={scan.id} className={`group relative overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl ${
                          theme === 'light' ? 'bg-gray-50 hover:bg-white' : 'bg-gray-700 hover:bg-gray-600'
                        }`}>
                          <div className="p-6">
                            {/* Scan Header */}
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex-1">
                                <h3 className={`text-lg font-bold mb-2 ${
                                  theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                                }`}>
                                  {scan.name}
                                </h3>
                                <div className="flex items-center mb-2">
                                  <FaCalendarAlt className={`text-sm mr-2 ${
                                    theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                                  }`} />
                                  <span className={`text-sm ${
                                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                                  }`}>
                                    {new Date(scan.date).toLocaleDateString()}
                                  </span>
                                </div>
                              </div>

                              {scan.isBaseline && (
                                <div className="flex items-center px-3 py-1 rounded-full bg-green-500/20 text-green-600">
                                  <FaCheckCircle className="text-xs mr-1" />
                                  <span className="text-xs font-semibold">Baseline</span>
                                </div>
                              )}
                            </div>

                            {/* Scan Details */}
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center justify-between">
                                <span className={`text-sm ${
                                  theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                                }`}>
                                  Phase:
                                </span>
                                <span className={`text-sm font-medium ${
                                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                                }`}>
                                  {scan.kvkPhase}
                                </span>
                              </div>

                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <FaUsers className={`text-sm mr-2 ${
                                    theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                                  }`} />
                                  <span className={`text-sm ${
                                    theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                                  }`}>
                                    Players:
                                  </span>
                                </div>
                                <span className={`text-sm font-bold ${
                                  theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                                }`}>
                                  {scan.players?.length || 0}
                                </span>
                              </div>
                            </div>

                            {/* Action Button */}
                            <Link
                              to={`/scans/${scan.id}`}
                              className={`group/btn relative w-full inline-flex items-center justify-center px-4 py-3 rounded-xl font-semibold transition-all duration-300 ${
                                theme === 'light'
                                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                                  : 'bg-blue-500 hover:bg-blue-600 text-white'
                              } shadow-lg hover:shadow-xl transform hover:scale-105`}
                            >
                              <FaEye className="mr-2 text-sm group-hover/btn:animate-pulse" />
                              View Details
                              <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover/btn:opacity-10 transition-opacity duration-300"></div>
                            </Link>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className={`p-8 text-center ${
                    theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                      theme === 'light' ? 'bg-gray-100' : 'bg-gray-700'
                    }`}>
                      <FaExclamationTriangle className="text-2xl" />
                    </div>
                    <p className="text-lg font-medium">No scans found for this KvK</p>
                    <p className="text-sm mt-2">Upload a scan to start tracking performance data.</p>
                  </div>
                )}
              </div>
            );
          })}

          {unassignedScans.length > 0 && (
            <div className={`mb-8 rounded-3xl shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-3xl ${
              theme === 'light' ? 'bg-white' : 'bg-gray-800'
            }`}>
              {/* General Scans Header */}
              <div className={`p-6 ${
                theme === 'light'
                  ? 'bg-gradient-to-r from-gray-600 to-slate-600'
                  : 'bg-gradient-to-r from-gray-700 to-slate-700'
              }`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-white/20 backdrop-blur-sm mr-4">
                    <FaFileAlt className="text-2xl text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      General Scans
                    </h2>
                    <p className="text-gray-100">
                      Scans not tied to any specific KvK • {unassignedScans.length} scan{unassignedScans.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
              </div>

              {/* General Scan Cards */}
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {unassignedScans.map((scan) => (
                    <div key={scan.id} className={`group relative overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl ${
                      theme === 'light' ? 'bg-gray-50 hover:bg-white' : 'bg-gray-700 hover:bg-gray-600'
                    }`}>
                      <div className="p-6">
                        {/* Scan Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className={`text-lg font-bold mb-2 ${
                              theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                            }`}>
                              {scan.name}
                            </h3>
                            <div className="flex items-center mb-2">
                              <FaCalendarAlt className={`text-sm mr-2 ${
                                theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                              }`} />
                              <span className={`text-sm ${
                                theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                              }`}>
                                {new Date(scan.date).toLocaleDateString()}
                              </span>
                            </div>
                          </div>

                          {scan.isBaseline && (
                            <div className="flex items-center px-3 py-1 rounded-full bg-green-500/20 text-green-600">
                              <FaCheckCircle className="text-xs mr-1" />
                              <span className="text-xs font-semibold">Baseline</span>
                            </div>
                          )}
                        </div>

                        {/* Scan Details */}
                        <div className="space-y-3 mb-6">
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${
                              theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              Phase:
                            </span>
                            <span className={`text-sm font-medium ${
                              theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                            }`}>
                              {scan.kvkPhase}
                            </span>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <FaUsers className={`text-sm mr-2 ${
                                theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                              }`} />
                              <span className={`text-sm ${
                                theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                              }`}>
                                Players:
                              </span>
                            </div>
                            <span className={`text-sm font-bold ${
                              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                            }`}>
                              {scan.players?.length || 0}
                            </span>
                          </div>
                        </div>

                        {/* Action Button */}
                        <Link
                          to={`/scans/${scan.id}`}
                          className={`group/btn relative w-full inline-flex items-center justify-center px-4 py-3 rounded-xl font-semibold transition-all duration-300 ${
                            theme === 'light'
                              ? 'bg-gray-600 hover:bg-gray-700 text-white'
                              : 'bg-gray-500 hover:bg-gray-600 text-white'
                          } shadow-lg hover:shadow-xl transform hover:scale-105`}
                        >
                          <FaEye className="mr-2 text-sm group-hover/btn:animate-pulse" />
                          View Details
                          <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover/btn:opacity-10 transition-opacity duration-300"></div>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </>
      )}
      </div>
    </div>
  );
};

export default ScansListPage;