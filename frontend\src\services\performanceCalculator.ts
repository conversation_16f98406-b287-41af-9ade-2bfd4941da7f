import { 
  ScanData, 
  PlayerScanData, 
  PerformanceMetrics, 
  AlliancePerformance, 
  KingdomPerformance,
  defaultThresholds,
  getGradeFromScore
} from '../types/dataTypes';

// Calculate performance metrics between two scans
export function calculatePerformanceMetrics(
  baselineScan: ScanData,
  currentScan: ScanData
): PerformanceMetrics[] {
  const metrics: PerformanceMetrics[] = [];

  // Process each player in the current scan
  currentScan.players.forEach(currentPlayer => {
    // Find the same player in the baseline scan
    const baselinePlayer = baselineScan.players.find(
      player => player.governorId === currentPlayer.governorId
    );

    // Skip if player wasn't in the baseline scan
    if (!baselinePlayer) return;

    // Calculate growth metrics
    const powerGrowth = currentPlayer.power - baselinePlayer.power;
    const killPointsGrowth = currentPlayer.killPoints - baselinePlayer.killPoints;
    const t1Growth = currentPlayer.t1Kills - baselinePlayer.t1Kills;
    const t2Growth = currentPlayer.t2Kills - baselinePlayer.t2Kills;
    const t3Growth = currentPlayer.t3Kills - baselinePlayer.t3Kills;
    const t4Growth = currentPlayer.t4Kills - baselinePlayer.t4Kills;
    const t5Growth = currentPlayer.t5Kills - baselinePlayer.t5Kills;
    const totalKillsGrowth = currentPlayer.totalKills - baselinePlayer.totalKills;
    const t45Growth = currentPlayer.t45Kills - baselinePlayer.t45Kills;
    const deadsGrowth = currentPlayer.deads - baselinePlayer.deads;
    const rssGatheredGrowth = currentPlayer.rssGathered - baselinePlayer.rssGathered;
    const rssAssistedGrowth = currentPlayer.rssAssisted - baselinePlayer.rssAssisted;
    const helpsGrowth = currentPlayer.helps - baselinePlayer.helps;
    const allianceHonorGrowth = 
      (currentPlayer.allianceHonor || 0) - (baselinePlayer.allianceHonor || 0);

    // Calculate KP per dead ratio
    const kpPerDead = deadsGrowth > 0 ? killPointsGrowth / deadsGrowth : 0;

    // Detect if player was zeroed
    const isZeroed = 
      powerGrowth <= -defaultThresholds.zeroedPowerDropAbs || 
      (powerGrowth < 0 && Math.abs(powerGrowth) / baselinePlayer.power >= defaultThresholds.zeroedPowerDropPct) &&
      deadsGrowth >= defaultThresholds.significantDeadForZeroed;

    // Calculate performance scores (0-100)
    const powerScore = calculateScore(
      powerGrowth, 
      defaultThresholds.power.min, 
      defaultThresholds.power.target, 
      defaultThresholds.power.excellent
    );
    
    const killsScore = calculateScore(
      killPointsGrowth, 
      defaultThresholds.killPoints.min, 
      defaultThresholds.killPoints.target, 
      defaultThresholds.killPoints.excellent
    );
    
    const t45KillsScore = calculateScore(
      t45Growth, 
      defaultThresholds.t4t5Kills.min, 
      defaultThresholds.t4t5Kills.target, 
      defaultThresholds.t4t5Kills.excellent
    );
    
    const deadsScore = calculateScore(
      deadsGrowth, 
      defaultThresholds.deadTroops.min, 
      defaultThresholds.deadTroops.target, 
      defaultThresholds.deadTroops.excellent
    );
    
    const assistanceScore = calculateScore(
      rssAssistedGrowth, 
      defaultThresholds.resourceAssistance.min, 
      defaultThresholds.resourceAssistance.target, 
      defaultThresholds.resourceAssistance.excellent
    );
    
    const activityScore = calculateScore(
      helpsGrowth, 
      defaultThresholds.helpCount.min, 
      defaultThresholds.helpCount.target, 
      defaultThresholds.helpCount.excellent
    );
    
    const kpPerDeadScore = calculateScore(
      kpPerDead, 
      defaultThresholds.kpPerDeadMin, 
      defaultThresholds.kpPerDeadTarget, 
      defaultThresholds.kpPerDeadExcellent
    );

    // Calculate overall score (weighted average)
    const overallScore = (
      powerScore * 0.15 + 
      killsScore * 0.25 + 
      t45KillsScore * 0.2 + 
      deadsScore * 0.15 + 
      assistanceScore * 0.1 + 
      activityScore * 0.1 + 
      kpPerDeadScore * 0.05
    );

    // Calculate grades from scores
    const powerGrade = getGradeFromScore(powerScore);
    const killsGrade = getGradeFromScore(killsScore);
    const t45KillsGrade = getGradeFromScore(t45KillsScore);
    const deadsGrade = getGradeFromScore(deadsScore);
    const assistanceGrade = getGradeFromScore(assistanceScore);
    const activityGrade = getGradeFromScore(activityScore);
    const overallGrade = getGradeFromScore(overallScore);
    const kpPerDeadGrade = getGradeFromScore(kpPerDeadScore);

    // Determine if player is underperforming
    const underperformanceReasons: string[] = [];
    if (powerScore < 50) underperformanceReasons.push('Low power growth');
    if (killsScore < 50) underperformanceReasons.push('Low kill points');
    if (t45KillsScore < 50) underperformanceReasons.push('Low T4/T5 kills');
    if (deadsScore < 50) underperformanceReasons.push('Low dead troops');
    if (assistanceScore < 50) underperformanceReasons.push('Low resource assistance');
    if (activityScore < 50) underperformanceReasons.push('Low activity');
    if (kpPerDeadScore < 50) underperformanceReasons.push('Poor KP per dead ratio');

    const isUnderperforming = overallScore < 60;

    // Create the performance metrics object
    const playerMetrics: PerformanceMetrics = {
      playerId: currentPlayer.governorId,
      playerName: currentPlayer.name,
      alliance: currentPlayer.alliance || 'Unknown',
      powerGrowth,
      killPointsGrowth,
      killsGrowth: {
        t1: t1Growth,
        t2: t2Growth,
        t3: t3Growth,
        t4: t4Growth,
        t5: t5Growth,
        total: totalKillsGrowth,
        t45: t45Growth
      },
      deadsGrowth,
      rssGatheredGrowth,
      rssAssistedGrowth,
      helpsGrowth,
      allianceHonorGrowth,
      kpPerDead,
      isZeroed,
      scores: {
        powerScore,
        killsScore,
        t45KillsScore,
        deadsScore,
        assistanceScore,
        activityScore,
        overallScore,
        kpPerDeadScore
      },
      grades: {
        powerGrade,
        killsGrade,
        t45KillsGrade,
        deadsGrade,
        assistanceGrade,
        activityGrade,
        overallGrade,
        kpPerDeadGrade
      },
      isUnderperforming,
      underperformanceReasons
    };

    metrics.push(playerMetrics);
  });

  return metrics;
}

// Helper function to calculate a score based on min, target, and excellent thresholds
function calculateScore(
  value: number,
  min: number,
  target: number,
  excellent: number
): number {
  if (value <= 0) return 0;
  if (value < min) return Math.floor((value / min) * 50);
  if (value < target) return Math.floor(50 + ((value - min) / (target - min)) * 30);
  if (value < excellent) return Math.floor(80 + ((value - target) / (excellent - target)) * 10);
  return 100;
}

// Calculate alliance performance metrics
export function calculateAlliancePerformance(
  playerMetrics: PerformanceMetrics[]
): AlliancePerformance[] {
  // Group players by alliance
  const allianceMap = new Map<string, PerformanceMetrics[]>();
  
  playerMetrics.forEach(player => {
    if (!allianceMap.has(player.alliance)) {
      allianceMap.set(player.alliance, []);
    }
    allianceMap.get(player.alliance)!.push(player);
  });

  // Calculate performance for each alliance
  return Array.from(allianceMap.entries()).map(([name, players]) => {
    const memberCount = players.length;
    
    // Calculate totals and averages
    const totalPower = players.reduce((sum, p) => sum + p.powerGrowth, 0);
    const totalKillPoints = players.reduce((sum, p) => sum + p.killPointsGrowth, 0);
    const totalDeads = players.reduce((sum, p) => sum + p.deadsGrowth, 0);
    const totalT45Kills = players.reduce((sum, p) => sum + p.killsGrowth.t45, 0);
    
    const averagePower = totalPower / memberCount;
    const averageKillPoints = totalKillPoints / memberCount;
    const averageDeads = totalDeads / memberCount;
    const averageT45Kills = totalT45Kills / memberCount;
    const averageScore = players.reduce((sum, p) => sum + p.scores.overallScore, 0) / memberCount;
    
    // Count underperforming players
    const underperformers = players.filter(p => p.isUnderperforming);
    const underperformingCount = underperformers.length;
    const underperformingPercentage = (underperformingCount / memberCount) * 100;
    
    // Get top performers (top 10% or at least 3)
    const topCount = Math.max(3, Math.ceil(memberCount * 0.1));
    const topPerformers = [...players]
      .sort((a, b) => b.scores.overallScore - a.scores.overallScore)
      .slice(0, topCount)
      .map(p => p.playerId);

    return {
      name,
      memberCount,
      averagePower,
      totalPower,
      averageKillPoints,
      totalKillPoints,
      averageDeads,
      totalDeads,
      averageT45Kills,
      totalT45Kills,
      averageScore,
      underperformingCount,
      underperformingPercentage,
      topPerformers,
      underperformers: underperformers.map(p => p.playerId)
    };
  });
}

// Calculate kingdom performance metrics
export function calculateKingdomPerformance(
  playerMetrics: PerformanceMetrics[],
  alliancePerformance: AlliancePerformance[]
): KingdomPerformance {
  const totalPlayers = playerMetrics.length;
  const activePlayers = playerMetrics.filter(p => 
    p.killPointsGrowth > 0 || p.deadsGrowth > 0 || p.powerGrowth > 0
  ).length;
  
  const totalPower = playerMetrics.reduce((sum, p) => sum + p.powerGrowth, 0);
  const totalKillPoints = playerMetrics.reduce((sum, p) => sum + p.killPointsGrowth, 0);
  const totalDeads = playerMetrics.reduce((sum, p) => sum + p.deadsGrowth, 0);
  const totalT45Kills = playerMetrics.reduce((sum, p) => sum + p.killsGrowth.t45, 0);
  
  const averageScore = playerMetrics.reduce((sum, p) => sum + p.scores.overallScore, 0) / totalPlayers;
  
  // Calculate grade distribution
  const gradeDistribution = {
    S: playerMetrics.filter(p => p.grades.overallGrade === 'S').length,
    A: playerMetrics.filter(p => p.grades.overallGrade === 'A').length,
    B: playerMetrics.filter(p => p.grades.overallGrade === 'B').length,
    C: playerMetrics.filter(p => p.grades.overallGrade === 'C').length,
    D: playerMetrics.filter(p => p.grades.overallGrade === 'D').length,
    F: playerMetrics.filter(p => p.grades.overallGrade === 'F').length
  };

  return {
    totalPlayers,
    activePlayers,
    totalPower,
    totalKillPoints,
    totalDeads,
    totalT45Kills,
    averageScore,
    gradeDistribution,
    alliancePerformance
  };
}
