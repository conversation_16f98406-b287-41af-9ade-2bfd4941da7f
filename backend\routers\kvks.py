from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud_kvk, schemas, models, services
from database import get_db

router = APIRouter(
    prefix="/kvks",
    tags=["KvKs"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=schemas.KvK, status_code=status.HTTP_201_CREATED)
def create_kvk(
    kvk: schemas.KvKCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new KvK.
    - **name**: Unique name for the KvK
    - **start_date**: When the KvK starts
    - **end_date**: When the KvK ends (optional)
    - **status**: Status of the KvK (upcoming, active, completed)
    - **season**: Season number
    """
    try:
        return crud_kvk.create_kvk(db=db, kvk=kvk)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@router.get("/", response_model=List[schemas.KvK])
def read_kvks(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Retrieve a list of all KvKs, ordered by most recent first.
    """
    kvks = crud_kvk.get_kvks(db, skip=skip, limit=limit)
    return kvks

@router.get("/active", response_model=schemas.KvK)
def read_active_kvk(db: Session = Depends(get_db)):
    """
    Get the currently active KvK.
    """
    kvk = crud_kvk.get_active_kvk(db)
    if kvk is None:
        raise HTTPException(status_code=404, detail="No active KvK found")
    return kvk

@router.get("/{kvk_id}", response_model=schemas.KvK)
def read_kvk(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific KvK by its ID.
    """
    db_kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return db_kvk

@router.put("/{kvk_id}", response_model=schemas.KvK)
def update_kvk(
    kvk_id: int,
    kvk_update: schemas.KvKUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing KvK.
    """
    db_kvk = crud_kvk.update_kvk(db, kvk_id=kvk_id, kvk_update=kvk_update)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return db_kvk

@router.delete("/{kvk_id}")
def delete_kvk(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a KvK.
    """
    db_kvk = crud_kvk.delete_kvk(db, kvk_id=kvk_id)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return {"message": "KvK deleted successfully"}

@router.patch("/{kvk_id}/status")
def update_kvk_status(
    kvk_id: int,
    status: str,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    """
    Update KvK status and optionally set end date.
    """
    if status not in ["upcoming", "active", "completed"]:
        raise HTTPException(status_code=400, detail="Invalid status. Must be 'upcoming', 'active', or 'completed'")

    db_kvk = crud_kvk.update_kvk_status(db, kvk_id=kvk_id, status=status, end_date=end_date)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return db_kvk

@router.get("/{kvk_id}/scans", response_model=List[schemas.ScanWithPlayerCount])
def read_kvk_scans(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Get all scans for a specific KvK.
    """
    # First check if KvK exists
    db_kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")

    scans = crud_kvk.get_kvk_scans(db, kvk_id=kvk_id)

    # Add player count to each scan
    scans_with_count = []
    for scan in scans:
        player_count = db.query(models.PlayerStat).filter(models.PlayerStat.scan_id == scan.id).count()
        scan_dict = {
            "id": scan.id,
            "name": scan.name,
            "is_baseline": scan.is_baseline,
            "kvk_id": scan.kvk_id,
            "kvk_phase": scan.kvk_phase,
            "timestamp": scan.timestamp,
            "player_count": player_count
        }
        scans_with_count.append(scan_dict)

    return scans_with_count

@router.get("/{kvk_id}/performance")
def get_kvk_performance(
    kvk_id: int,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """
    Get comprehensive KvK performance analysis with live data.
    """
    try:
        # Check if KvK exists
        db_kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
        if db_kvk is None:
            raise HTTPException(status_code=404, detail="KvK not found")

        # Get KvK performance summary
        performance_data = services.get_kvk_performance_summary(db, kvk_id=kvk_id, limit=limit)

        return performance_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting KvK performance: {str(e)}")

@router.get("/{kvk_id}/summary")
def get_kvk_summary(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Get KvK summary statistics for the card display.
    """
    try:
        # Check if KvK exists
        db_kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
        if db_kvk is None:
            raise HTTPException(status_code=404, detail="KvK not found")

        # Get all scans for this KvK
        kvk_scans = crud_kvk.get_kvk_scans(db, kvk_id=kvk_id)
        if not kvk_scans:
            return {
                "kvk_id": kvk_id,
                "total_kill_points": "TBD",
                "total_dead_troops": "TBD",
                "total_t45_kills": "TBD",
                "total_players": "Available",
                "has_data": False
            }

        # Find baseline and latest scans
        baseline_scan = None
        latest_scan = None

        for scan in kvk_scans:
            if scan.is_baseline:
                baseline_scan = scan
            if not latest_scan or scan.timestamp > latest_scan.timestamp:
                latest_scan = scan

        if not baseline_scan or not latest_scan:
            return {
                "kvk_id": kvk_id,
                "total_kill_points": "TBD",
                "total_dead_troops": "TBD",
                "total_t45_kills": "TBD",
                "total_players": "Available",
                "has_data": False
            }

        # Calculate summary stats
        if baseline_scan.id == latest_scan.id:
            # Only baseline scan exists
            total_kp = sum(stat.total_kill_points or 0 for stat in baseline_scan.player_stats)
            total_dead = sum(stat.dead_troops or 0 for stat in baseline_scan.player_stats)
            total_t45 = sum((stat.kill_points_t4 or 0) + (stat.kill_points_t5 or 0) for stat in baseline_scan.player_stats)
        else:
            # Calculate gains from baseline to latest
            delta_stats = services.calculate_delta_stats(db, latest_scan, baseline_scan)
            total_kp = sum(ds.kill_points_delta for ds in delta_stats if ds.kill_points_delta > 0)
            total_dead = sum(ds.dead_troops_delta for ds in delta_stats if ds.dead_troops_delta > 0)

            # Calculate T4-5 kills gains
            total_t45 = 0
            for ds in delta_stats:
                if ds.player:
                    # Get current and baseline T4-5 kills
                    current_stat = next((s for s in latest_scan.player_stats if s.player_id == ds.player_id), None)
                    baseline_stat = next((s for s in baseline_scan.player_stats if s.player_id == ds.player_id), None)

                    if current_stat and baseline_stat:
                        current_t45 = (current_stat.kill_points_t4 or 0) + (current_stat.kill_points_t5 or 0)
                        baseline_t45 = (baseline_stat.kill_points_t4 or 0) + (baseline_stat.kill_points_t5 or 0)
                        total_t45 += max(0, current_t45 - baseline_t45)

        return {
            "kvk_id": kvk_id,
            "total_kill_points": total_kp,
            "total_dead_troops": total_dead,
            "total_t45_kills": total_t45,
            "total_players": len(latest_scan.player_stats),
            "has_data": True,
            "baseline_scan_id": baseline_scan.id,
            "latest_scan_id": latest_scan.id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting KvK summary: {str(e)}")