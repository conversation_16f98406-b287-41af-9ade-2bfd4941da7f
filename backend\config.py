"""
Production-ready configuration management.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings with validation and environment variable support."""
    
    # Database Configuration
    database_url: str = "sqlite:///./kvk_tracker.db"
    db_pool_size: int = 20
    db_max_overflow: int = 30
    db_pool_timeout: int = 30
    db_pool_recycle: int = 3600
    
    # Security Configuration
    secret_key: str = "fallback-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Application Configuration
    debug: bool = False
    log_level: str = "INFO"
    environment: str = "development"
    
    # CORS Configuration
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    
    # Rate Limiting Configuration
    max_requests_per_minute: int = 60
    max_login_attempts_per_hour: int = 5
    max_upload_size_mb: int = 100
    
    # Cache Configuration
    cache_default_ttl: int = 300
    cache_scan_data_ttl: int = 600
    cache_player_data_ttl: int = 300
    cache_kvk_data_ttl: int = 900
    cache_reports_ttl: int = 180
    
    # File Upload Configuration
    upload_dir: str = "uploads"
    max_file_size_mb: int = 100
    allowed_file_extensions: List[str] = [".xlsx", ".xls", ".csv"]
    
    # Monitoring Configuration
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # Email Configuration
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_from_email: Optional[str] = None
    
    # External API Configuration
    external_api_timeout: int = 30
    external_api_retries: int = 3
    
    # Backup Configuration
    backup_enabled: bool = True
    backup_schedule: str = "0 2 * * *"  # Daily at 2 AM
    backup_retention_days: int = 30
    backup_location: str = "backups/"
    
    # Security Headers
    enable_security_headers: bool = True
    enable_rate_limiting: bool = True
    enable_cors: bool = True
    
    @validator('allowed_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    @validator('allowed_file_extensions', pre=True)
    def parse_file_extensions(cls, v):
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(',')]
        return v
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        if v == "fallback-secret-key-change-in-production" and os.getenv("ENVIRONMENT") == "production":
            raise ValueError("SECRET_KEY must be changed in production environment")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator('database_url')
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of: {', '.join(valid_levels)}")
        return v.upper()
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_environments = ['development', 'staging', 'production']
        if v.lower() not in valid_environments:
            raise ValueError(f"ENVIRONMENT must be one of: {', '.join(valid_environments)}")
        return v.lower()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Map environment variables to settings
        fields = {
            'database_url': {'env': 'DATABASE_URL'},
            'secret_key': {'env': 'SECRET_KEY'},
            'algorithm': {'env': 'ALGORITHM'},
            'access_token_expire_minutes': {'env': 'ACCESS_TOKEN_EXPIRE_MINUTES'},
            'debug': {'env': 'DEBUG'},
            'log_level': {'env': 'LOG_LEVEL'},
            'environment': {'env': 'ENVIRONMENT'},
            'allowed_origins': {'env': 'ALLOWED_ORIGINS'},
            'max_requests_per_minute': {'env': 'MAX_REQUESTS_PER_MINUTE'},
            'max_login_attempts_per_hour': {'env': 'MAX_LOGIN_ATTEMPTS_PER_HOUR'},
            'max_upload_size_mb': {'env': 'MAX_UPLOAD_SIZE_MB'},
            'cache_default_ttl': {'env': 'CACHE_DEFAULT_TTL'},
            'cache_scan_data_ttl': {'env': 'CACHE_SCAN_DATA_TTL'},
            'cache_player_data_ttl': {'env': 'CACHE_PLAYER_DATA_TTL'},
            'cache_kvk_data_ttl': {'env': 'CACHE_KVK_DATA_TTL'},
            'cache_reports_ttl': {'env': 'CACHE_REPORTS_TTL'},
            'db_pool_size': {'env': 'DB_POOL_SIZE'},
            'db_max_overflow': {'env': 'DB_MAX_OVERFLOW'},
            'db_pool_timeout': {'env': 'DB_POOL_TIMEOUT'},
            'db_pool_recycle': {'env': 'DB_POOL_RECYCLE'},
            'upload_dir': {'env': 'UPLOAD_DIR'},
            'max_file_size_mb': {'env': 'MAX_FILE_SIZE_MB'},
            'allowed_file_extensions': {'env': 'ALLOWED_FILE_EXTENSIONS'},
            'enable_metrics': {'env': 'ENABLE_METRICS'},
            'metrics_port': {'env': 'METRICS_PORT'},
            'smtp_host': {'env': 'SMTP_HOST'},
            'smtp_port': {'env': 'SMTP_PORT'},
            'smtp_username': {'env': 'SMTP_USERNAME'},
            'smtp_password': {'env': 'SMTP_PASSWORD'},
            'smtp_from_email': {'env': 'SMTP_FROM_EMAIL'},
            'external_api_timeout': {'env': 'EXTERNAL_API_TIMEOUT'},
            'external_api_retries': {'env': 'EXTERNAL_API_RETRIES'},
            'backup_enabled': {'env': 'BACKUP_ENABLED'},
            'backup_schedule': {'env': 'BACKUP_SCHEDULE'},
            'backup_retention_days': {'env': 'BACKUP_RETENTION_DAYS'},
            'backup_location': {'env': 'BACKUP_LOCATION'},
        }

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()

# Convenience function to get specific setting values
def get_database_url() -> str:
    return get_settings().database_url

def get_secret_key() -> str:
    return get_settings().secret_key

def is_production() -> bool:
    return get_settings().environment == "production"

def is_development() -> bool:
    return get_settings().environment == "development"

def get_cors_origins() -> List[str]:
    return get_settings().allowed_origins

def get_upload_config() -> dict:
    settings = get_settings()
    return {
        "upload_dir": settings.upload_dir,
        "max_file_size_mb": settings.max_file_size_mb,
        "allowed_extensions": settings.allowed_file_extensions
    }

def get_cache_config() -> dict:
    settings = get_settings()
    return {
        "default_ttl": settings.cache_default_ttl,
        "scan_data_ttl": settings.cache_scan_data_ttl,
        "player_data_ttl": settings.cache_player_data_ttl,
        "kvk_data_ttl": settings.cache_kvk_data_ttl,
        "reports_ttl": settings.cache_reports_ttl
    }

def get_db_config() -> dict:
    settings = get_settings()
    return {
        "database_url": settings.database_url,
        "pool_size": settings.db_pool_size,
        "max_overflow": settings.db_max_overflow,
        "pool_timeout": settings.db_pool_timeout,
        "pool_recycle": settings.db_pool_recycle
    }

# Validate configuration on import
try:
    _settings = get_settings()
    print(f"✅ Configuration loaded successfully for {_settings.environment} environment")
except Exception as e:
    print(f"❌ Configuration validation failed: {e}")
    raise
