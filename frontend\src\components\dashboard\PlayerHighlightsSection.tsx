import React, { memo } from 'react'; // Import memo
import { useTheme } from '../../contexts/ThemeContext';
import { FaArrowUp, FaSkull<PERSON><PERSON>bones, FaFistRaised, FaShieldAlt } from 'react-icons/fa';
import { PlayerScanData } from '../../types/dataTypes';

interface PlayerHighlightCardProps {
  title: string;
  player: PlayerScanData | null; // Allow player to be null
  valueKey: keyof PlayerScanData; // e.g., 'power', 'killPoints'
  icon: React.ReactElement;
  formatter: (num: number, abbreviated?: boolean) => string;
  theme: string; // Explicitly pass theme
  valuePrefix?: string;
}

const HighlightCard: React.FC<PlayerHighlightCardProps> = ({ title, player, valueKey, icon, formatter, theme, valuePrefix = "" }) => {
  if (!player) {
    return (
      <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} transition-shadow duration-300`}>
        <div className="flex items-center mb-3">
          {React.cloneElement(icon, { className: `h-8 w-8 mr-3 ${theme === 'light' ? 'text-gray-400' : 'text-gray-500'}` })}
          <div>
            <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'}`}>{title}</h3>
            <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>N/A</p>
          </div>
        </div>
        <p className={`text-3xl font-bold ${theme === 'light' ? 'text-gray-400' : 'text-gray-500'}`}>N/A</p>
      </div>
    );
  }

  const value = player[valueKey] as number; // Type assertion

  return (
    <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
      <div className="flex items-center mb-3">
        {React.cloneElement(icon, { className: `h-8 w-8 mr-3 ${icon.props.className}` })} {/* Preserve original icon color classes */}
        <div>
          <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'}`}>{title}</h3>
          <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>{player.name} [{player.alliance || 'N/A'}]</p>
        </div>
      </div>
      <p className={`text-3xl font-bold ${icon.props.className?.includes('green') ? (theme === 'light' ? 'text-green-600' : 'text-green-400') : icon.props.className?.includes('red') ? (theme === 'light' ? 'text-red-600' : 'text-red-400') : icon.props.className?.includes('purple') ? (theme === 'light' ? 'text-purple-600' : 'text-purple-400') : (theme === 'light' ? 'text-yellow-600' : 'text-yellow-400')}`}>
        {valuePrefix}{formatter(value, true)}
      </p>
    </div>
  );
};


interface PlayerHighlightsSectionProps {
  topPowerGainer: PlayerScanData | null;
  topKillsGainer: PlayerScanData | null;
  topT45KillsGainer: PlayerScanData | null;
  topDeadsGainer: PlayerScanData | null;
  formatter: (num: number, abbreviated?: boolean) => string;
}

const PlayerHighlightsSection: React.FC<PlayerHighlightsSectionProps> = ({
  topPowerGainer,
  topKillsGainer,
  topT45KillsGainer,
  topDeadsGainer,
  formatter,
}) => {
  const { theme } = useTheme();

  return (
    <section className="mb-10">
      <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>Player Performance Highlights</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <HighlightCard
          title="Top Power Gainer"
          player={topPowerGainer}
          valueKey="power" // Assuming 'power' is the key for power gain in Player type
          icon={<FaArrowUp className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-green-500' : 'text-green-400'}`} />}
          formatter={formatter}
          theme={theme}
          valuePrefix="+"
        />
        <HighlightCard
          title="Top Kills Gainer"
          player={topKillsGainer}
          valueKey="killPoints" // Assuming 'killPoints' is the key
          icon={<FaFistRaised className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} />}
          formatter={formatter}
          theme={theme}
          valuePrefix="+"
        />
        <HighlightCard
          title="Top T4/T5 Kills"
          player={topT45KillsGainer}
          valueKey="t45Kills" // Assuming 't45Kills' is the key
          icon={<FaShieldAlt className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-purple-500' : 'text-purple-400'}`} />}
          formatter={formatter}
          theme={theme}
          valuePrefix="+"
        />
        <HighlightCard
          title="Most Deads"
          player={topDeadsGainer}
          valueKey="deads" // Assuming 'deads' is the key
          icon={<FaSkullCrossbones className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400'}`} />}
          formatter={formatter}
          theme={theme}
        />
      </div>
    </section>
  );
};

export default memo(PlayerHighlightsSection); // Wrap with memo
